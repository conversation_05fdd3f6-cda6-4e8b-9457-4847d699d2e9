{"ast": null, "code": "import * as React from 'react';\nimport { useState, useCallback, useMemo, useContext } from 'react';\nimport { MapContext } from './map';\nexport const MountedMapsContext = React.createContext(null);\nexport const MapProvider = props => {\n  const [maps, setMaps] = useState({});\n  const onMapMount = useCallback((map, id = 'default') => {\n    setMaps(currMaps => {\n      if (id === 'current') {\n        throw new Error(\"'current' cannot be used as map id\");\n      }\n      if (currMaps[id]) {\n        throw new Error(`Multiple maps with the same id: ${id}`);\n      }\n      return {\n        ...currMaps,\n        [id]: map\n      };\n    });\n  }, []);\n  const onMapUnmount = useCallback((id = 'default') => {\n    setMaps(currMaps => {\n      if (currMaps[id]) {\n        const nextMaps = {\n          ...currMaps\n        };\n        delete nextMaps[id];\n        return nextMaps;\n      }\n      return currMaps;\n    });\n  }, []);\n  return React.createElement(MountedMapsContext.Provider, {\n    value: {\n      maps,\n      onMapMount,\n      onMapUnmount\n    }\n  }, props.children);\n};\nexport function useMap() {\n  var _a;\n  const maps = (_a = useContext(MountedMapsContext)) === null || _a === void 0 ? void 0 : _a.maps;\n  const currentMap = useContext(MapContext);\n  const mapsWithCurrent = useMemo(() => {\n    return {\n      ...maps,\n      current: currentMap === null || currentMap === void 0 ? void 0 : currentMap.map\n    };\n  }, [maps, currentMap]);\n  return mapsWithCurrent;\n}", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useMemo", "useContext", "MapContext", "MountedMapsContext", "createContext", "MapProvider", "props", "maps", "setMaps", "onMapMount", "map", "id", "currMaps", "Error", "onMapUnmount", "nextMaps", "createElement", "Provider", "value", "children", "useMap", "_a", "currentMap", "mapsWithCurrent", "current"], "sources": ["C:/Users/<USER>/Downloads/morocco-complete-map-geo<PERSON><PERSON>-topojson-main/morocco-vector-tiles/node_modules/react-map-gl/dist/esm/components/use-map.js"], "sourcesContent": ["import * as React from 'react';\nimport { useState, useCallback, useMemo, useContext } from 'react';\nimport { MapContext } from './map';\nexport const MountedMapsContext = React.createContext(null);\nexport const MapProvider = props => {\n    const [maps, setMaps] = useState({});\n    const onMapMount = useCallback((map, id = 'default') => {\n        setMaps(currMaps => {\n            if (id === 'current') {\n                throw new Error(\"'current' cannot be used as map id\");\n            }\n            if (currMaps[id]) {\n                throw new Error(`Multiple maps with the same id: ${id}`);\n            }\n            return { ...currMaps, [id]: map };\n        });\n    }, []);\n    const onMapUnmount = useCallback((id = 'default') => {\n        setMaps(currMaps => {\n            if (currMaps[id]) {\n                const nextMaps = { ...currMaps };\n                delete nextMaps[id];\n                return nextMaps;\n            }\n            return currMaps;\n        });\n    }, []);\n    return (React.createElement(MountedMapsContext.Provider, { value: {\n            maps,\n            onMapMount,\n            onMapUnmount\n        } }, props.children));\n};\nexport function useMap() {\n    var _a;\n    const maps = (_a = useContext(MountedMapsContext)) === null || _a === void 0 ? void 0 : _a.maps;\n    const currentMap = useContext(MapContext);\n    const mapsWithCurrent = useMemo(() => {\n        return { ...maps, current: currentMap === null || currentMap === void 0 ? void 0 : currentMap.map };\n    }, [maps, currentMap]);\n    return mapsWithCurrent;\n}\n//# sourceMappingURL=use-map.js.map"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,EAAEC,UAAU,QAAQ,OAAO;AAClE,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAO,MAAMC,kBAAkB,GAAGN,KAAK,CAACO,aAAa,CAAC,IAAI,CAAC;AAC3D,OAAO,MAAMC,WAAW,GAAGC,KAAK,IAAI;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC,MAAMW,UAAU,GAAGV,WAAW,CAAC,CAACW,GAAG,EAAEC,EAAE,GAAG,SAAS,KAAK;IACpDH,OAAO,CAACI,QAAQ,IAAI;MAChB,IAAID,EAAE,KAAK,SAAS,EAAE;QAClB,MAAM,IAAIE,KAAK,CAAC,oCAAoC,CAAC;MACzD;MACA,IAAID,QAAQ,CAACD,EAAE,CAAC,EAAE;QACd,MAAM,IAAIE,KAAK,CAAC,mCAAmCF,EAAE,EAAE,CAAC;MAC5D;MACA,OAAO;QAAE,GAAGC,QAAQ;QAAE,CAACD,EAAE,GAAGD;MAAI,CAAC;IACrC,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EACN,MAAMI,YAAY,GAAGf,WAAW,CAAC,CAACY,EAAE,GAAG,SAAS,KAAK;IACjDH,OAAO,CAACI,QAAQ,IAAI;MAChB,IAAIA,QAAQ,CAACD,EAAE,CAAC,EAAE;QACd,MAAMI,QAAQ,GAAG;UAAE,GAAGH;QAAS,CAAC;QAChC,OAAOG,QAAQ,CAACJ,EAAE,CAAC;QACnB,OAAOI,QAAQ;MACnB;MACA,OAAOH,QAAQ;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EACN,OAAQf,KAAK,CAACmB,aAAa,CAACb,kBAAkB,CAACc,QAAQ,EAAE;IAAEC,KAAK,EAAE;MAC1DX,IAAI;MACJE,UAAU;MACVK;IACJ;EAAE,CAAC,EAAER,KAAK,CAACa,QAAQ,CAAC;AAC5B,CAAC;AACD,OAAO,SAASC,MAAMA,CAAA,EAAG;EACrB,IAAIC,EAAE;EACN,MAAMd,IAAI,GAAG,CAACc,EAAE,GAAGpB,UAAU,CAACE,kBAAkB,CAAC,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACd,IAAI;EAC/F,MAAMe,UAAU,GAAGrB,UAAU,CAACC,UAAU,CAAC;EACzC,MAAMqB,eAAe,GAAGvB,OAAO,CAAC,MAAM;IAClC,OAAO;MAAE,GAAGO,IAAI;MAAEiB,OAAO,EAAEF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACZ;IAAI,CAAC;EACvG,CAAC,EAAE,CAACH,IAAI,EAAEe,UAAU,CAAC,CAAC;EACtB,OAAOC,eAAe;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}