{"ast": null, "code": "import * as React from 'react';\nimport { default as _Map } from './components/map';\nimport { default as _Marker } from './components/marker';\nimport { default as _Popup } from './components/popup';\nimport { default as _AttributionControl } from './components/attribution-control';\nimport { default as _FullscreenControl } from './components/fullscreen-control';\nimport { default as _GeolocateControl } from './components/geolocate-control';\nimport { default as _NavigationControl } from './components/navigation-control';\nimport { default as _ScaleControl } from './components/scale-control';\nimport { default as _Layer } from './components/layer';\nimport { default as _Source } from './components/source';\nimport { useMap as _useMap } from './components/use-map';\nexport function useMap() {\n  return _useMap();\n}\nconst mapLib = import('mapbox-gl');\nexport const Map = (() => {\n  return React.forwardRef(function Map(props, ref) {\n    return _Map(props, ref, mapLib);\n  });\n})();\nexport const Marker = _Marker;\nexport const Popup = _Popup;\nexport const AttributionControl = _AttributionControl;\nexport const FullscreenControl = _FullscreenControl;\nexport const NavigationControl = _NavigationControl;\nexport const GeolocateControl = _GeolocateControl;\nexport const ScaleControl = _ScaleControl;\nexport const Layer = _Layer;\nexport const Source = _Source;\nexport { default as useControl } from './components/use-control';\nexport { MapProvider } from './components/use-map';\nexport default Map;\n// Types\nexport * from './types/public';\nexport * from './types/style-spec-mapbox';", "map": {"version": 3, "names": ["React", "default", "_Map", "_Marker", "_Popup", "_AttributionControl", "_FullscreenControl", "_GeolocateControl", "_NavigationControl", "_ScaleControl", "_Layer", "_Source", "useMap", "_useMap", "mapLib", "Map", "forwardRef", "props", "ref", "<PERSON><PERSON>", "Popup", "AttributionControl", "FullscreenControl", "NavigationControl", "GeolocateControl", "ScaleControl", "Layer", "Source", "useControl", "MapProvider"], "sources": ["../../src/exports-mapbox.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAgB9B,SAAQC,OAAO,IAAIC,IAAI,QAA8B,kBAAkB;AACvE,SAAQD,OAAO,IAAIE,OAAO,QAAoC,qBAAqB;AACnF,SAAQF,OAAO,IAAIG,MAAM,QAAkC,oBAAoB;AAC/E,SACEH,OAAO,IAAII,mBAAmB,QAEzB,kCAAkC;AACzC,SACEJ,OAAO,IAAIK,kBAAkB,QAExB,iCAAiC;AACxC,SACEL,OAAO,IAAIM,iBAAiB,QAEvB,gCAAgC;AACvC,SACEN,OAAO,IAAIO,kBAAkB,QAExB,iCAAiC;AACxC,SACEP,OAAO,IAAIQ,aAAa,QAEnB,4BAA4B;AACnC,SAAQR,OAAO,IAAIS,MAAM,QAAkC,oBAAoB;AAC/E,SAAQT,OAAO,IAAIU,OAAO,QAAoC,qBAAqB;AACnF,SAAQC,MAAM,IAAIC,OAAO,QAAO,sBAAsB;AAKtD,OAAM,SAAUD,MAAMA,CAAA;EACpB,OAAOC,OAAO,EAAa;AAC7B;AAIA,MAAMC,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC;AAClC,OAAO,MAAMC,GAAG,GAAG,CAAC,MAAK;EACvB,OAAOf,KAAK,CAACgB,UAAU,CAAC,SAASD,GAAGA,CAACE,KAAe,EAAEC,GAAsB;IAC1E,OAAOhB,IAAI,CAAmDe,KAAK,EAAEC,GAAG,EAAEJ,MAAM,CAAC;EACnF,CAAC,CAAC;AACJ,CAAC,EAAC,CAAE;AAGJ,OAAO,MAAMK,MAAM,GAAGhB,OAEQ;AAG9B,OAAO,MAAMiB,KAAK,GAAGhB,MAES;AAI9B,OAAO,MAAMiB,kBAAkB,GAAGhB,mBAEJ;AAI9B,OAAO,MAAMiB,iBAAiB,GAAGhB,kBAEH;AAI9B,OAAO,MAAMiB,iBAAiB,GAAGf,kBAEH;AAO9B,OAAO,MAAMgB,gBAAgB,GAAGjB,iBAEF;AAI9B,OAAO,MAAMkB,YAAY,GAAGhB,aAEE;AAG9B,OAAO,MAAMiB,KAAK,GAAGhB,MAA0D;AAG/E,OAAO,MAAMiB,MAAM,GAAGhB,OAA4D;AAElF,SAAQV,OAAO,IAAI2B,UAAU,QAAO,0BAA0B;AAC9D,SAAQC,WAAW,QAAO,sBAAsB;AAEhD,eAAed,GAAG;AAElB;AACA,cAAc,gBAAgB;AAgB9B,cAAc,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}