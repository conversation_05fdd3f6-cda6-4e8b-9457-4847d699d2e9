{"ast": null, "code": "import { useEffect, memo } from 'react';\nimport { applyReactStyle } from '../utils/apply-react-style';\nimport useControl from './use-control';\nfunction FullscreenControl(props) {\n  const ctrl = useControl(({\n    mapLib\n  }) => new mapLib.FullscreenControl({\n    container: props.containerId && document.getElementById(props.containerId)\n  }), {\n    position: props.position\n  });\n  useEffect(() => {\n    applyReactStyle(ctrl._controlContainer, props.style);\n  }, [props.style]);\n  return null;\n}\nexport default memo(FullscreenControl);", "map": {"version": 3, "names": ["useEffect", "memo", "applyReactStyle", "useControl", "FullscreenControl", "props", "ctrl", "mapLib", "container", "containerId", "document", "getElementById", "position", "_controlContainer", "style"], "sources": ["../../../src/components/fullscreen-control.tsx"], "sourcesContent": [null], "mappings": "AAEA,SAAQA,SAAS,EAAEC,IAAI,QAAO,OAAO;AACrC,SAAQC,eAAe,QAAO,4BAA4B;AAC1D,OAAOC,UAAU,MAAM,eAAe;AActC,SAASC,iBAAiBA,CACxBC,KAAuD;EAEvD,MAAMC,IAAI,GAAGH,UAAU,CACrB,CAAC;IAACI;EAAM,CAAC,KACP,IAAIA,MAAM,CAACH,iBAAiB,CAAC;IAC3BI,SAAS,EAAEH,KAAK,CAACI,WAAW,IAAIC,QAAQ,CAACC,cAAc,CAACN,KAAK,CAACI,WAAW;GAC1E,CAAa,EAChB;IAACG,QAAQ,EAAEP,KAAK,CAACO;EAAQ,CAAC,CAC3B;EAEDZ,SAAS,CAAC,MAAK;IACbE,eAAe,CAACI,IAAI,CAACO,iBAAiB,EAAER,KAAK,CAACS,KAAK,CAAC;EACtD,CAAC,EAAE,CAACT,KAAK,CAACS,KAAK,CAAC,CAAC;EAEjB,OAAO,IAAI;AACb;AAEA,eAAeb,IAAI,CAACG,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}