import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ll<PERSON><PERSON><PERSON>, FillExtrusionLayer, <PERSON>map<PERSON>ayer, <PERSON><PERSON>e<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ay<PERSON>, SymbolLayer } from 'mapbox-gl';
export declare type AnyLayer = BackgroundLayer | CircleLayer | FillExtrusionLayer | FillLayer | HeatmapLayer | HillshadeLayer | LineLayer | RasterLayer | SymbolLayer | SkyLayer;
export type { BackgroundLayer, SkyLayer, CircleLayer, FillLayer, FillExtrusionLayer, HeatmapLayer, HillshadeLayer, LineLayer, RasterLayer, SymbolLayer };
import type { GeoJSONSourceRaw, VideoSourceRaw, ImageSourceRaw, VectorSource as VectorSourceRaw, RasterSource, CanvasSourceRaw, RasterDemSource } from 'mapbox-gl';
export declare type AnySource = GeoJSONSourceRaw | VideoSourceRaw | ImageSourceRaw | CanvasSourceRaw | VectorSourceRaw | RasterSource | RasterDemSource;
export type { GeoJSONSourceRaw, VideoSourceRaw, ImageSourceRaw, CanvasSourceRaw, VectorSourceRaw, RasterSource, RasterDemSource };
export type { Style as MapStyle, Light, Fog, TerrainSpecification as Terrain, Projection } from 'mapbox-gl';
