{"ast": null, "code": "import * as React from 'react';\nimport { useState, useRef, useEffect, useContext, useMemo, useImperativeHandle } from 'react';\nimport { MountedMapsContext } from './use-map';\nimport Mapbox from '../mapbox/mapbox';\nimport createRef from '../mapbox/create-ref';\nimport useIsomorphicLayoutEffect from '../utils/use-isomorphic-layout-effect';\nimport setGlobals from '../utils/set-globals';\nexport const MapContext = React.createContext(null);\nexport default function Map(props, ref, defaultLib) {\n  const mountedMapsContext = useContext(MountedMapsContext);\n  const [mapInstance, setMapInstance] = useState(null);\n  const containerRef = useRef();\n  const {\n    current: contextValue\n  } = useRef({\n    mapLib: null,\n    map: null\n  });\n  useEffect(() => {\n    const mapLib = props.mapLib;\n    let isMounted = true;\n    let mapbox;\n    Promise.resolve(mapLib || defaultLib).then(module => {\n      if (!isMounted) {\n        return;\n      }\n      if (!module) {\n        throw new Error('Invalid mapLib');\n      }\n      const mapboxgl = 'Map' in module ? module : module.default;\n      if (!mapboxgl.Map) {\n        throw new Error('Invalid mapLib');\n      }\n      // workerUrl & workerClass may change the result of supported()\n      // https://github.com/visgl/react-map-gl/discussions/2027\n      setGlobals(mapboxgl, props);\n      if (!mapboxgl.supported || mapboxgl.supported(props)) {\n        if (props.reuseMaps) {\n          mapbox = Mapbox.reuse(props, containerRef.current);\n        }\n        if (!mapbox) {\n          mapbox = new Mapbox(mapboxgl.Map, props, containerRef.current);\n        }\n        contextValue.map = createRef(mapbox);\n        contextValue.mapLib = mapboxgl;\n        setMapInstance(mapbox);\n        mountedMapsContext === null || mountedMapsContext === void 0 ? void 0 : mountedMapsContext.onMapMount(contextValue.map, props.id);\n      } else {\n        throw new Error('Map is not supported by this browser');\n      }\n    }).catch(error => {\n      const {\n        onError\n      } = props;\n      if (onError) {\n        onError({\n          type: 'error',\n          target: null,\n          originalEvent: null,\n          error\n        });\n      } else {\n        console.error(error); // eslint-disable-line\n      }\n    });\n    return () => {\n      isMounted = false;\n      if (mapbox) {\n        mountedMapsContext === null || mountedMapsContext === void 0 ? void 0 : mountedMapsContext.onMapUnmount(props.id);\n        if (props.reuseMaps) {\n          mapbox.recycle();\n        } else {\n          mapbox.destroy();\n        }\n      }\n    };\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    if (mapInstance) {\n      mapInstance.setProps(props);\n    }\n  });\n  useImperativeHandle(ref, () => contextValue.map, [mapInstance]);\n  const style = useMemo(() => ({\n    position: 'relative',\n    width: '100%',\n    height: '100%',\n    ...props.style\n  }), [props.style]);\n  const CHILD_CONTAINER_STYLE = {\n    height: '100%'\n  };\n  return React.createElement(\"div\", {\n    id: props.id,\n    ref: containerRef,\n    style: style\n  }, mapInstance && React.createElement(MapContext.Provider, {\n    value: contextValue\n  }, React.createElement(\"div\", {\n    \"mapboxgl-children\": \"\",\n    style: CHILD_CONTAINER_STYLE\n  }, props.children)));\n}", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useContext", "useMemo", "useImperativeHandle", "MountedMapsContext", "Mapbox", "createRef", "useIsomorphicLayoutEffect", "setGlobals", "MapContext", "createContext", "Map", "props", "ref", "defaultLib", "mountedMapsContext", "mapInstance", "setMapInstance", "containerRef", "current", "contextValue", "mapLib", "map", "isMounted", "mapbox", "Promise", "resolve", "then", "module", "Error", "mapboxgl", "default", "supported", "reuseMaps", "reuse", "onMapMount", "id", "catch", "error", "onError", "type", "target", "originalEvent", "console", "onMapUnmount", "recycle", "destroy", "setProps", "style", "position", "width", "height", "CHILD_CONTAINER_STYLE", "createElement", "Provider", "value", "children"], "sources": ["../../../src/components/map.tsx"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAQC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,EAAEC,mBAAmB,QAAO,OAAO;AAE3F,SAAQC,kBAAkB,QAAO,WAAW;AAC5C,OAAOC,MAAqB,MAAM,kBAAkB;AACpD,OAAOC,SAAmB,MAAM,sBAAsB;AAGtD,OAAOC,yBAAyB,MAAM,uCAAuC;AAC7E,OAAOC,UAA4B,MAAM,sBAAsB;AAQ/D,OAAO,MAAMC,UAAU,GAAGZ,KAAK,CAACa,aAAa,CAAkB,IAAI,CAAC;AAwBpE,eAAc,SAAUC,GAAGA,CAMzBC,KAAqD,EACrDC,GAA4B,EAC5BC,UAAgD;EAEhD,MAAMC,kBAAkB,GAAGd,UAAU,CAACG,kBAAkB,CAAC;EACzD,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAmC,IAAI,CAAC;EACtF,MAAMoB,YAAY,GAAGnB,MAAM,EAAE;EAE7B,MAAM;IAACoB,OAAO,EAAEC;EAAY,CAAC,GAAGrB,MAAM,CAAwB;IAACsB,MAAM,EAAE,IAAI;IAAEC,GAAG,EAAE;EAAI,CAAC,CAAC;EAExFtB,SAAS,CAAC,MAAK;IACb,MAAMqB,MAAM,GAAGT,KAAK,CAACS,MAAM;IAC3B,IAAIE,SAAS,GAAG,IAAI;IACpB,IAAIC,MAAwC;IAE5CC,OAAO,CAACC,OAAO,CAACL,MAAM,IAAIP,UAAU,CAAC,CAClCa,IAAI,CAAEC,MAA8C,IAAI;MACvD,IAAI,CAACL,SAAS,EAAE;QACd;;MAEF,IAAI,CAACK,MAAM,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,gBAAgB,CAAC;;MAEnC,MAAMC,QAAQ,GAAG,KAAK,IAAIF,MAAM,GAAGA,MAAM,GAAGA,MAAM,CAACG,OAAO;MAC1D,IAAI,CAACD,QAAQ,CAACnB,GAAG,EAAE;QACjB,MAAM,IAAIkB,KAAK,CAAC,gBAAgB,CAAC;;MAGnC;MACA;MACArB,UAAU,CAACsB,QAAQ,EAAElB,KAAK,CAAC;MAC3B,IAAI,CAACkB,QAAQ,CAACE,SAAS,IAAIF,QAAQ,CAACE,SAAS,CAACpB,KAAK,CAAC,EAAE;QACpD,IAAIA,KAAK,CAACqB,SAAS,EAAE;UACnBT,MAAM,GAAGnB,MAAM,CAAC6B,KAAK,CAACtB,KAAK,EAAEM,YAAY,CAACC,OAAO,CAAC;;QAEpD,IAAI,CAACK,MAAM,EAAE;UACXA,MAAM,GAAG,IAAInB,MAAM,CAACyB,QAAQ,CAACnB,GAAG,EAAEC,KAAK,EAAEM,YAAY,CAACC,OAAO,CAAC;;QAEhEC,YAAY,CAACE,GAAG,GAAGhB,SAAS,CAACkB,MAAM,CAAC;QACpCJ,YAAY,CAACC,MAAM,GAAGS,QAAQ;QAE9Bb,cAAc,CAACO,MAAM,CAAC;QACtBT,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEoB,UAAU,CAACf,YAAY,CAACE,GAAG,EAAEV,KAAK,CAACwB,EAAE,CAAC;OAC3D,MAAM;QACL,MAAM,IAAIP,KAAK,CAAC,sCAAsC,CAAC;;IAE3D,CAAC,CAAC,CACDQ,KAAK,CAACC,KAAK,IAAG;MACb,MAAM;QAACC;MAAO,CAAC,GAAG3B,KAAK;MACvB,IAAI2B,OAAO,EAAE;QACXA,OAAO,CAAC;UACNC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE,IAAI;UACZC,aAAa,EAAE,IAAI;UACnBJ;SACD,CAAC;OACH,MAAM;QACLK,OAAO,CAACL,KAAK,CAACA,KAAK,CAAC,CAAC,CAAC;;IAE1B,CAAC,CAAC;IAEJ,OAAO,MAAK;MACVf,SAAS,GAAG,KAAK;MACjB,IAAIC,MAAM,EAAE;QACVT,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAE6B,YAAY,CAAChC,KAAK,CAACwB,EAAE,CAAC;QAC1C,IAAIxB,KAAK,CAACqB,SAAS,EAAE;UACnBT,MAAM,CAACqB,OAAO,EAAE;SACjB,MAAM;UACLrB,MAAM,CAACsB,OAAO,EAAE;;;IAGtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENvC,yBAAyB,CAAC,MAAK;IAC7B,IAAIS,WAAW,EAAE;MACfA,WAAW,CAAC+B,QAAQ,CAACnC,KAAK,CAAC;;EAE/B,CAAC,CAAC;EAEFT,mBAAmB,CAACU,GAAG,EAAE,MAAMO,YAAY,CAACE,GAAG,EAAE,CAACN,WAAW,CAAC,CAAC;EAE/D,MAAMgC,KAAK,GAAkB9C,OAAO,CAClC,OAAO;IACL+C,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACd,GAAGvC,KAAK,CAACoC;GACV,CAAC,EACF,CAACpC,KAAK,CAACoC,KAAK,CAAC,CACd;EAED,MAAMI,qBAAqB,GAAG;IAC5BD,MAAM,EAAE;GACT;EAED,OACEtD,KAAA,CAAAwD,aAAA;IAAKjB,EAAE,EAAExB,KAAK,CAACwB,EAAE;IAAEvB,GAAG,EAAEK,YAAY;IAAE8B,KAAK,EAAEA;EAAK,GAC/ChC,WAAW,IACVnB,KAAA,CAAAwD,aAAA,CAAC5C,UAAU,CAAC6C,QAAQ;IAACC,KAAK,EAAEnC;EAAY,GACtCvB,KAAA,CAAAwD,aAAA;IAAA,qBAAuB,EAAE;IAACL,KAAK,EAAEI;EAAqB,GACnDxC,KAAK,CAAC4C,QAAQ,CACX,CAET,CACG;AAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}