{"ast": null, "code": "/** These methods may break the react binding if called directly */\nconst skipMethods = ['setMaxBounds', 'setMinZoom', 'setMaxZoom', 'setMinPitch', 'setMaxPitch', 'setRenderWorldCopies', 'setProjection', 'setStyle', 'addSource', 'removeSource', 'addLayer', 'removeLayer', 'setLayerZoomRange', 'setFilter', 'setPaintProperty', 'setLayoutProperty', 'setLight', 'setTerrain', 'setFog', 'remove'];\nexport default function createRef(mapInstance) {\n  if (!mapInstance) {\n    return null;\n  }\n  const map = mapInstance.map;\n  const result = {\n    getMap: () => map,\n    // Overwrite getters to use our shadow transform\n    getCenter: () => mapInstance.transform.center,\n    getZoom: () => mapInstance.transform.zoom,\n    getBearing: () => mapInstance.transform.bearing,\n    getPitch: () => mapInstance.transform.pitch,\n    getPadding: () => mapInstance.transform.padding,\n    getBounds: () => mapInstance.transform.getBounds(),\n    project: lnglat => {\n      const tr = map.transform;\n      map.transform = mapInstance.transform;\n      const result = map.project(lnglat);\n      map.transform = tr;\n      return result;\n    },\n    unproject: point => {\n      const tr = map.transform;\n      map.transform = mapInstance.transform;\n      const result = map.unproject(point);\n      map.transform = tr;\n      return result;\n    },\n    // options diverge between mapbox and maplibre\n    queryTerrainElevation: (lnglat, options) => {\n      const tr = map.transform;\n      map.transform = mapInstance.transform;\n      const result = map.queryTerrainElevation(lnglat, options);\n      map.transform = tr;\n      return result;\n    },\n    queryRenderedFeatures: (geometry, options) => {\n      const tr = map.transform;\n      map.transform = mapInstance.transform;\n      const result = map.queryRenderedFeatures(geometry, options);\n      map.transform = tr;\n      return result;\n    }\n  };\n  for (const key of getMethodNames(map)) {\n    // @ts-expect-error\n    if (!(key in result) && !skipMethods.includes(key)) {\n      result[key] = map[key].bind(map);\n    }\n  }\n  return result;\n}\nfunction getMethodNames(obj) {\n  const result = new Set();\n  let proto = obj;\n  while (proto) {\n    for (const key of Object.getOwnPropertyNames(proto)) {\n      if (key[0] !== '_' && typeof obj[key] === 'function' && key !== 'fire' && key !== 'setEventedParent') {\n        result.add(key);\n      }\n    }\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Array.from(result);\n}", "map": {"version": 3, "names": ["skipMethods", "createRef", "mapInstance", "map", "result", "getMap", "getCenter", "transform", "center", "getZoom", "zoom", "getBearing", "bearing", "get<PERSON><PERSON>", "pitch", "getPadding", "padding", "getBounds", "project", "lnglat", "tr", "unproject", "point", "queryTerrainElevation", "options", "queryRenderedFeatures", "geometry", "key", "getMethodNames", "includes", "bind", "obj", "Set", "proto", "Object", "getOwnPropertyNames", "add", "getPrototypeOf", "Array", "from"], "sources": ["C:\\Users\\<USER>\\Downloads\\morocco-complete-map-geojson-topojson-main\\morocco-vector-tiles\\node_modules\\react-map-gl\\src\\mapbox\\create-ref.ts"], "sourcesContent": ["import type {\n  MapInstance,\n  MapInstanceInternal,\n  MapStyle,\n  Callbacks,\n  LngLatLike,\n  PointLike\n} from '../types';\nimport type Mapbox from './mapbox';\n\n/** These methods may break the react binding if called directly */\nconst skipMethods = [\n  'setMaxBounds',\n  'setMinZoom',\n  'setMaxZoom',\n  'setMinPitch',\n  'setMaxPitch',\n  'setRenderWorldCopies',\n  'setProjection',\n  'setStyle',\n  'addSource',\n  'removeSource',\n  'addLayer',\n  'removeLayer',\n  'setLayerZoomRange',\n  'setFilter',\n  'setPaintProperty',\n  'setLayoutProperty',\n  'setLight',\n  'setTerrain',\n  'setFog',\n  'remove'\n] as const;\n\nexport type MapRef<MapT extends MapInstance> = {\n  getMap(): MapT;\n} & Omit<MapT, typeof skipMethods[number]>;\n\nexport default function createRef<\n  StyleT extends MapStyle,\n  CallbacksT extends Callbacks,\n  MapT extends MapInstance\n>(mapInstance: Mapbox<StyleT, CallbacksT, MapT>): MapRef<MapT> | null {\n  if (!mapInstance) {\n    return null;\n  }\n\n  const map = mapInstance.map as MapInstanceInternal<MapT>;\n  const result: any = {\n    getMap: () => map,\n\n    // Overwrite getters to use our shadow transform\n    getCenter: () => mapInstance.transform.center,\n    getZoom: () => mapInstance.transform.zoom,\n    getBearing: () => mapInstance.transform.bearing,\n    getPitch: () => mapInstance.transform.pitch,\n    getPadding: () => mapInstance.transform.padding,\n    getBounds: () => mapInstance.transform.getBounds(),\n    project: (lnglat: LngLatLike) => {\n      const tr = map.transform;\n      map.transform = mapInstance.transform;\n      const result = map.project(lnglat);\n      map.transform = tr;\n      return result;\n    },\n    unproject: (point: PointLike) => {\n      const tr = map.transform;\n      map.transform = mapInstance.transform;\n      const result = map.unproject(point);\n      map.transform = tr;\n      return result;\n    },\n    // options diverge between mapbox and maplibre\n    queryTerrainElevation: (lnglat: LngLatLike, options?: any) => {\n      const tr = map.transform;\n      map.transform = mapInstance.transform;\n      const result = map.queryTerrainElevation(lnglat, options);\n      map.transform = tr;\n      return result;\n    },\n    queryRenderedFeatures: (geometry?: any, options?: any) => {\n      const tr = map.transform;\n      map.transform = mapInstance.transform;\n      const result = map.queryRenderedFeatures(geometry, options);\n      map.transform = tr;\n      return result;\n    }\n  };\n\n  for (const key of getMethodNames(map)) {\n    // @ts-expect-error\n    if (!(key in result) && !skipMethods.includes(key)) {\n      result[key] = map[key].bind(map);\n    }\n  }\n\n  return result;\n}\n\nfunction getMethodNames(obj: Object) {\n  const result = new Set<string>();\n\n  let proto = obj;\n  while (proto) {\n    for (const key of Object.getOwnPropertyNames(proto)) {\n      if (\n        key[0] !== '_' &&\n        typeof obj[key] === 'function' &&\n        key !== 'fire' &&\n        key !== 'setEventedParent'\n      ) {\n        result.add(key);\n      }\n    }\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Array.from(result);\n}\n"], "mappings": "AAUA;AACA,MAAMA,WAAW,GAAG,CAClB,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,aAAa,EACb,sBAAsB,EACtB,eAAe,EACf,UAAU,EACV,WAAW,EACX,cAAc,EACd,UAAU,EACV,aAAa,EACb,mBAAmB,EACnB,WAAW,EACX,kBAAkB,EAClB,mBAAmB,EACnB,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,CACA;AAMV,eAAc,SAAUC,SAASA,CAI/BC,WAA6C;EAC7C,IAAI,CAACA,WAAW,EAAE;IAChB,OAAO,IAAI;;EAGb,MAAMC,GAAG,GAAGD,WAAW,CAACC,GAAgC;EACxD,MAAMC,MAAM,GAAQ;IAClBC,MAAM,EAAEA,CAAA,KAAMF,GAAG;IAEjB;IACAG,SAAS,EAAEA,CAAA,KAAMJ,WAAW,CAACK,SAAS,CAACC,MAAM;IAC7CC,OAAO,EAAEA,CAAA,KAAMP,WAAW,CAACK,SAAS,CAACG,IAAI;IACzCC,UAAU,EAAEA,CAAA,KAAMT,WAAW,CAACK,SAAS,CAACK,OAAO;IAC/CC,QAAQ,EAAEA,CAAA,KAAMX,WAAW,CAACK,SAAS,CAACO,KAAK;IAC3CC,UAAU,EAAEA,CAAA,KAAMb,WAAW,CAACK,SAAS,CAACS,OAAO;IAC/CC,SAAS,EAAEA,CAAA,KAAMf,WAAW,CAACK,SAAS,CAACU,SAAS,EAAE;IAClDC,OAAO,EAAGC,MAAkB,IAAI;MAC9B,MAAMC,EAAE,GAAGjB,GAAG,CAACI,SAAS;MACxBJ,GAAG,CAACI,SAAS,GAAGL,WAAW,CAACK,SAAS;MACrC,MAAMH,MAAM,GAAGD,GAAG,CAACe,OAAO,CAACC,MAAM,CAAC;MAClChB,GAAG,CAACI,SAAS,GAAGa,EAAE;MAClB,OAAOhB,MAAM;IACf,CAAC;IACDiB,SAAS,EAAGC,KAAgB,IAAI;MAC9B,MAAMF,EAAE,GAAGjB,GAAG,CAACI,SAAS;MACxBJ,GAAG,CAACI,SAAS,GAAGL,WAAW,CAACK,SAAS;MACrC,MAAMH,MAAM,GAAGD,GAAG,CAACkB,SAAS,CAACC,KAAK,CAAC;MACnCnB,GAAG,CAACI,SAAS,GAAGa,EAAE;MAClB,OAAOhB,MAAM;IACf,CAAC;IACD;IACAmB,qBAAqB,EAAEA,CAACJ,MAAkB,EAAEK,OAAa,KAAI;MAC3D,MAAMJ,EAAE,GAAGjB,GAAG,CAACI,SAAS;MACxBJ,GAAG,CAACI,SAAS,GAAGL,WAAW,CAACK,SAAS;MACrC,MAAMH,MAAM,GAAGD,GAAG,CAACoB,qBAAqB,CAACJ,MAAM,EAAEK,OAAO,CAAC;MACzDrB,GAAG,CAACI,SAAS,GAAGa,EAAE;MAClB,OAAOhB,MAAM;IACf,CAAC;IACDqB,qBAAqB,EAAEA,CAACC,QAAc,EAAEF,OAAa,KAAI;MACvD,MAAMJ,EAAE,GAAGjB,GAAG,CAACI,SAAS;MACxBJ,GAAG,CAACI,SAAS,GAAGL,WAAW,CAACK,SAAS;MACrC,MAAMH,MAAM,GAAGD,GAAG,CAACsB,qBAAqB,CAACC,QAAQ,EAAEF,OAAO,CAAC;MAC3DrB,GAAG,CAACI,SAAS,GAAGa,EAAE;MAClB,OAAOhB,MAAM;IACf;GACD;EAED,KAAK,MAAMuB,GAAG,IAAIC,cAAc,CAACzB,GAAG,CAAC,EAAE;IACrC;IACA,IAAI,EAAEwB,GAAG,IAAIvB,MAAM,CAAC,IAAI,CAACJ,WAAW,CAAC6B,QAAQ,CAACF,GAAG,CAAC,EAAE;MAClDvB,MAAM,CAACuB,GAAG,CAAC,GAAGxB,GAAG,CAACwB,GAAG,CAAC,CAACG,IAAI,CAAC3B,GAAG,CAAC;;;EAIpC,OAAOC,MAAM;AACf;AAEA,SAASwB,cAAcA,CAACG,GAAW;EACjC,MAAM3B,MAAM,GAAG,IAAI4B,GAAG,EAAU;EAEhC,IAAIC,KAAK,GAAGF,GAAG;EACf,OAAOE,KAAK,EAAE;IACZ,KAAK,MAAMN,GAAG,IAAIO,MAAM,CAACC,mBAAmB,CAACF,KAAK,CAAC,EAAE;MACnD,IACEN,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IACd,OAAOI,GAAG,CAACJ,GAAG,CAAC,KAAK,UAAU,IAC9BA,GAAG,KAAK,MAAM,IACdA,GAAG,KAAK,kBAAkB,EAC1B;QACAvB,MAAM,CAACgC,GAAG,CAACT,GAAG,CAAC;;;IAGnBM,KAAK,GAAGC,MAAM,CAACG,cAAc,CAACJ,KAAK,CAAC;;EAEtC,OAAOK,KAAK,CAACC,IAAI,CAACnC,MAAM,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}