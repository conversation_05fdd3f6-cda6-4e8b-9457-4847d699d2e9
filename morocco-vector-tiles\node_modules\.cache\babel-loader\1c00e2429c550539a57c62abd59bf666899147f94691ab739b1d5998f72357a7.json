{"ast": null, "code": "export default function assert(condition, message) {\n  if (!condition) {\n    throw new Error(message);\n  }\n}", "map": {"version": 3, "names": ["assert", "condition", "message", "Error"], "sources": ["C:\\Users\\<USER>\\Downloads\\morocco-complete-map-geojson-topojson-main\\morocco-vector-tiles\\node_modules\\react-map-gl\\src\\utils\\assert.ts"], "sourcesContent": ["export default function assert(condition: any, message: string) {\n  if (!condition) {\n    throw new Error(message);\n  }\n}\n"], "mappings": "AAAA,eAAc,SAAUA,MAAMA,CAACC,SAAc,EAAEC,OAAe;EAC5D,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAIE,KAAK,CAACD,OAAO,CAAC;;AAE5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}