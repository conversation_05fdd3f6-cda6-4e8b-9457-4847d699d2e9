{"ast": null, "code": "/* global document */\nimport * as React from 'react';\nimport { createPortal } from 'react-dom';\nimport { useImperativeHandle, useEffect, useMemo, useRef, useContext, forwardRef, memo } from 'react';\nimport { applyReactStyle } from '../utils/apply-react-style';\nimport { MapContext } from './map';\nimport { arePointsEqual } from '../utils/deep-equal';\n/* eslint-disable complexity,max-statements */\nfunction Marker(props, ref) {\n  const {\n    map,\n    mapLib\n  } = useContext(MapContext);\n  const thisRef = useRef({\n    props\n  });\n  thisRef.current.props = props;\n  const marker = useMemo(() => {\n    let hasChildren = false;\n    React.Children.forEach(props.children, el => {\n      if (el) {\n        hasChildren = true;\n      }\n    });\n    const options = {\n      ...props,\n      element: hasChildren ? document.createElement('div') : null\n    };\n    const mk = new mapLib.Marker(options);\n    mk.setLngLat([props.longitude, props.latitude]);\n    mk.getElement().addEventListener('click', e => {\n      var _a, _b;\n      (_b = (_a = thisRef.current.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a, {\n        type: 'click',\n        target: mk,\n        originalEvent: e\n      });\n    });\n    mk.on('dragstart', e => {\n      var _a, _b;\n      const evt = e;\n      evt.lngLat = marker.getLngLat();\n      (_b = (_a = thisRef.current.props).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, evt);\n    });\n    mk.on('drag', e => {\n      var _a, _b;\n      const evt = e;\n      evt.lngLat = marker.getLngLat();\n      (_b = (_a = thisRef.current.props).onDrag) === null || _b === void 0 ? void 0 : _b.call(_a, evt);\n    });\n    mk.on('dragend', e => {\n      var _a, _b;\n      const evt = e;\n      evt.lngLat = marker.getLngLat();\n      (_b = (_a = thisRef.current.props).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, evt);\n    });\n    return mk;\n  }, []);\n  useEffect(() => {\n    marker.addTo(map.getMap());\n    return () => {\n      marker.remove();\n    };\n  }, []);\n  const {\n    longitude,\n    latitude,\n    offset,\n    style,\n    draggable = false,\n    popup = null,\n    rotation = 0,\n    rotationAlignment = 'auto',\n    pitchAlignment = 'auto'\n  } = props;\n  useEffect(() => {\n    applyReactStyle(marker.getElement(), style);\n  }, [style]);\n  useImperativeHandle(ref, () => marker, []);\n  if (marker.getLngLat().lng !== longitude || marker.getLngLat().lat !== latitude) {\n    marker.setLngLat([longitude, latitude]);\n  }\n  if (offset && !arePointsEqual(marker.getOffset(), offset)) {\n    marker.setOffset(offset);\n  }\n  if (marker.isDraggable() !== draggable) {\n    marker.setDraggable(draggable);\n  }\n  if (marker.getRotation() !== rotation) {\n    marker.setRotation(rotation);\n  }\n  if (marker.getRotationAlignment() !== rotationAlignment) {\n    marker.setRotationAlignment(rotationAlignment);\n  }\n  if (marker.getPitchAlignment() !== pitchAlignment) {\n    marker.setPitchAlignment(pitchAlignment);\n  }\n  if (marker.getPopup() !== popup) {\n    marker.setPopup(popup);\n  }\n  return createPortal(props.children, marker.getElement());\n}\nexport default memo(forwardRef(Marker));", "map": {"version": 3, "names": ["React", "createPortal", "useImperativeHandle", "useEffect", "useMemo", "useRef", "useContext", "forwardRef", "memo", "applyReactStyle", "MapContext", "arePointsEqual", "<PERSON><PERSON>", "props", "ref", "map", "mapLib", "thisRef", "current", "marker", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Children", "for<PERSON>ach", "children", "el", "options", "element", "document", "createElement", "mk", "setLngLat", "longitude", "latitude", "getElement", "addEventListener", "e", "_b", "_a", "onClick", "call", "type", "target", "originalEvent", "on", "evt", "lngLat", "getLngLat", "onDragStart", "onDrag", "onDragEnd", "addTo", "getMap", "remove", "offset", "style", "draggable", "popup", "rotation", "rotationAlignment", "pitchAlignment", "lng", "lat", "getOffset", "setOffset", "isDraggable", "setDraggable", "getRotation", "setRotation", "getRotationAlignment", "setRotationAlignment", "getPitchAlignment", "setPitchAlignment", "getPopup", "setPopup"], "sources": ["../../../src/components/marker.ts"], "sourcesContent": [null], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAQC,YAAY,QAAO,WAAW;AACtC,SAAQC,mBAAmB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,IAAI,QAAO,OAAO;AACnG,SAAQC,eAAe,QAAO,4BAA4B;AAI1D,SAAQC,UAAU,QAAO,OAAO;AAChC,SAAQC,cAAc,QAAO,qBAAqB;AAyBlD;AACA,SAASC,MAAMA,CACbC,KAA0C,EAC1CC,GAAuB;EAEvB,MAAM;IAACC,GAAG;IAAEC;EAAM,CAAC,GAAGV,UAAU,CAACI,UAAU,CAAC;EAC5C,MAAMO,OAAO,GAAGZ,MAAM,CAAC;IAACQ;EAAK,CAAC,CAAC;EAC/BI,OAAO,CAACC,OAAO,CAACL,KAAK,GAAGA,KAAK;EAE7B,MAAMM,MAAM,GAAYf,OAAO,CAAC,MAAK;IACnC,IAAIgB,WAAW,GAAG,KAAK;IACvBpB,KAAK,CAACqB,QAAQ,CAACC,OAAO,CAACT,KAAK,CAACU,QAAQ,EAAEC,EAAE,IAAG;MAC1C,IAAIA,EAAE,EAAE;QACNJ,WAAW,GAAG,IAAI;;IAEtB,CAAC,CAAC;IACF,MAAMK,OAAO,GAAG;MACd,GAAGZ,KAAK;MACRa,OAAO,EAAEN,WAAW,GAAGO,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,GAAG;KACxD;IAED,MAAMC,EAAE,GAAG,IAAIb,MAAM,CAACJ,MAAM,CAACa,OAAO,CAAY;IAChDI,EAAE,CAACC,SAAS,CAAC,CAACjB,KAAK,CAACkB,SAAS,EAAElB,KAAK,CAACmB,QAAQ,CAAC,CAAC;IAE/CH,EAAE,CAACI,UAAU,EAAE,CAACC,gBAAgB,CAAC,OAAO,EAAGC,CAAa,IAAI;;MAC1D,CAAAC,EAAA,IAAAC,EAAA,GAAApB,OAAO,CAACC,OAAO,CAACL,KAAK,EAACyB,OAAO,cAAAF,EAAA,uBAAAA,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAG;QAC9BG,IAAI,EAAE,OAAO;QACbC,MAAM,EAAEZ,EAAE;QACVa,aAAa,EAAEP;OAChB,CAAC;IACJ,CAAC,CAAC;IAEFN,EAAE,CAACc,EAAE,CAAC,WAAW,EAAER,CAAC,IAAG;;MACrB,MAAMS,GAAG,GAAGT,CAA6B;MACzCS,GAAG,CAACC,MAAM,GAAG1B,MAAM,CAAC2B,SAAS,EAAE;MAC/B,CAAAV,EAAA,IAAAC,EAAA,GAAApB,OAAO,CAACC,OAAO,CAACL,KAAK,EAACkC,WAAW,cAAAX,EAAA,uBAAAA,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAGO,GAAG,CAAC;IAC1C,CAAC,CAAC;IACFf,EAAE,CAACc,EAAE,CAAC,MAAM,EAAER,CAAC,IAAG;;MAChB,MAAMS,GAAG,GAAGT,CAA6B;MACzCS,GAAG,CAACC,MAAM,GAAG1B,MAAM,CAAC2B,SAAS,EAAE;MAC/B,CAAAV,EAAA,IAAAC,EAAA,GAAApB,OAAO,CAACC,OAAO,CAACL,KAAK,EAACmC,MAAM,cAAAZ,EAAA,uBAAAA,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAGO,GAAG,CAAC;IACrC,CAAC,CAAC;IACFf,EAAE,CAACc,EAAE,CAAC,SAAS,EAAER,CAAC,IAAG;;MACnB,MAAMS,GAAG,GAAGT,CAA6B;MACzCS,GAAG,CAACC,MAAM,GAAG1B,MAAM,CAAC2B,SAAS,EAAE;MAC/B,CAAAV,EAAA,IAAAC,EAAA,GAAApB,OAAO,CAACC,OAAO,CAACL,KAAK,EAACoC,SAAS,cAAAb,EAAA,uBAAAA,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAGO,GAAG,CAAC;IACxC,CAAC,CAAC;IAEF,OAAOf,EAAE;EACX,CAAC,EAAE,EAAE,CAAC;EAEN1B,SAAS,CAAC,MAAK;IACbgB,MAAM,CAAC+B,KAAK,CAACnC,GAAG,CAACoC,MAAM,EAAE,CAAC;IAE1B,OAAO,MAAK;MACVhC,MAAM,CAACiC,MAAM,EAAE;IACjB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IACJrB,SAAS;IACTC,QAAQ;IACRqB,MAAM;IACNC,KAAK;IACLC,SAAS,GAAG,KAAK;IACjBC,KAAK,GAAG,IAAI;IACZC,QAAQ,GAAG,CAAC;IACZC,iBAAiB,GAAG,MAAM;IAC1BC,cAAc,GAAG;EAAM,CACxB,GAAG9C,KAAK;EAETV,SAAS,CAAC,MAAK;IACbM,eAAe,CAACU,MAAM,CAACc,UAAU,EAAE,EAAEqB,KAAK,CAAC;EAC7C,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEXpD,mBAAmB,CAACY,GAAG,EAAE,MAAMK,MAAM,EAAE,EAAE,CAAC;EAE1C,IAAIA,MAAM,CAAC2B,SAAS,EAAE,CAACc,GAAG,KAAK7B,SAAS,IAAIZ,MAAM,CAAC2B,SAAS,EAAE,CAACe,GAAG,KAAK7B,QAAQ,EAAE;IAC/Eb,MAAM,CAACW,SAAS,CAAC,CAACC,SAAS,EAAEC,QAAQ,CAAC,CAAC;;EAEzC,IAAIqB,MAAM,IAAI,CAAC1C,cAAc,CAACQ,MAAM,CAAC2C,SAAS,EAAE,EAAET,MAAM,CAAC,EAAE;IACzDlC,MAAM,CAAC4C,SAAS,CAACV,MAAM,CAAC;;EAE1B,IAAIlC,MAAM,CAAC6C,WAAW,EAAE,KAAKT,SAAS,EAAE;IACtCpC,MAAM,CAAC8C,YAAY,CAACV,SAAS,CAAC;;EAEhC,IAAIpC,MAAM,CAAC+C,WAAW,EAAE,KAAKT,QAAQ,EAAE;IACrCtC,MAAM,CAACgD,WAAW,CAACV,QAAQ,CAAC;;EAE9B,IAAItC,MAAM,CAACiD,oBAAoB,EAAE,KAAKV,iBAAiB,EAAE;IACvDvC,MAAM,CAACkD,oBAAoB,CAACX,iBAAiB,CAAC;;EAEhD,IAAIvC,MAAM,CAACmD,iBAAiB,EAAE,KAAKX,cAAc,EAAE;IACjDxC,MAAM,CAACoD,iBAAiB,CAACZ,cAAc,CAAC;;EAE1C,IAAIxC,MAAM,CAACqD,QAAQ,EAAE,KAAKhB,KAAK,EAAE;IAC/BrC,MAAM,CAACsD,QAAQ,CAACjB,KAAK,CAAC;;EAGxB,OAAOvD,YAAY,CAACY,KAAK,CAACU,QAAQ,EAAEJ,MAAM,CAACc,UAAU,EAAE,CAAC;AAC1D;AAEA,eAAezB,IAAI,CAACD,UAAU,CAACK,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}