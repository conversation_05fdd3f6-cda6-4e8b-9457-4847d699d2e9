{"version": 3, "file": "exports-maplibre.js", "sourceRoot": "", "sources": ["../../src/exports-maplibre.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAgB/B,OAAO,EAAC,OAAO,IAAI,IAAI,EAAwB,MAAM,kBAAkB,CAAC;AACxE,OAAO,EAAC,OAAO,IAAI,OAAO,EAA8B,MAAM,qBAAqB,CAAC;AACpF,OAAO,EAAC,OAAO,IAAI,MAAM,EAA4B,MAAM,oBAAoB,CAAC;AAChF,OAAO,EACL,OAAO,IAAI,mBAAmB,EAE/B,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EACL,OAAO,IAAI,kBAAkB,EAE9B,MAAM,iCAAiC,CAAC;AACzC,OAAO,EACL,OAAO,IAAI,iBAAiB,EAE7B,MAAM,gCAAgC,CAAC;AACxC,OAAO,EACL,OAAO,IAAI,kBAAkB,EAE9B,MAAM,iCAAiC,CAAC;AACzC,OAAO,EACL,OAAO,IAAI,aAAa,EAEzB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAC,OAAO,IAAI,MAAM,EAA4B,MAAM,oBAAoB,CAAC;AAChF,OAAO,EAAC,OAAO,IAAI,OAAO,EAA8B,MAAM,qBAAqB,CAAC;AACpF,OAAO,EAAC,MAAM,IAAI,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAKvD,MAAM,UAAU,MAAM;IACpB,OAAO,OAAO,EAAe,CAAC;AAChC,CAAC;AAID,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AACrC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,EAAE;IACvB,OAAO,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,KAAe,EAAE,GAAsB;QAC1E,OAAO,IAAI,CAAkD,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;IACnF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,EAAE,CAAC;AAGL,MAAM,CAAC,MAAM,MAAM,GAAG,OAEQ,CAAC;AAG/B,MAAM,CAAC,MAAM,KAAK,GAAG,MAES,CAAC;AAI/B,MAAM,CAAC,MAAM,kBAAkB,GAAG,mBAEJ,CAAC;AAI/B,MAAM,CAAC,MAAM,iBAAiB,GAAG,kBAEH,CAAC;AAI/B,MAAM,CAAC,MAAM,iBAAiB,GAAG,kBAEH,CAAC;AAO/B,MAAM,CAAC,MAAM,gBAAgB,GAAG,iBAEF,CAAC;AAI/B,MAAM,CAAC,MAAM,YAAY,GAAG,aAEE,CAAC;AAG/B,MAAM,CAAC,MAAM,KAAK,GAAG,MAA0D,CAAC;AAGhF,MAAM,CAAC,MAAM,MAAM,GAAG,OAA4D,CAAC;AAEnF,OAAO,EAAC,OAAO,IAAI,UAAU,EAAC,MAAM,0BAA0B,CAAC;AAC/D,OAAO,EAAC,WAAW,EAAC,MAAM,sBAAsB,CAAC;AAEjD,eAAe,GAAG,CAAC;AAEnB,QAAQ;AACR,cAAc,gBAAgB,CAAC;AAgB/B,cAAc,6BAA6B,CAAC"}