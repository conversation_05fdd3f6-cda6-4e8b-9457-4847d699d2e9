{"name": "react-map-gl", "description": "React components for Mapbox GL JS-compatible libraries", "version": "7.1.7", "keywords": ["mapbox", "mapbox-gl", "react", "react-mapbox-gl", "react mapbox"], "repository": {"type": "git", "url": "https://github.com/visgl/react-map-gl.git"}, "license": "MIT", "types": "dist/esm/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "files": ["src", "dist", "maplibre", "README.md"], "scripts": {"typecheck": "tsc -p tsconfig.esm.json --noEmit", "bootstrap": "PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true yarn && ocular-bootstrap", "build": "ocular-clean && tsc -b tsconfig.esm.json && tsc -b tsconfig.es5.json", "lint": "ocular-lint", "cover": "ocular-test cover", "publish-prod": "ocular-publish prod", "publish-beta": "ocular-publish beta", "test": "yarn typecheck && ocular-test", "test-fast": "yarn typecheck && ocular-test node", "metrics": "ocular-metrics", "update-release-branch": "scripts/update-release-branch.sh"}, "dependencies": {"@maplibre/maplibre-gl-style-spec": "^19.2.1", "@types/mapbox-gl": ">=1.0.0"}, "peerDependencies": {"mapbox-gl": ">=1.13.0", "maplibre-gl": ">=1.13.0", "react": ">=16.3.0", "react-dom": ">=16.3.0"}, "peerDependenciesMeta": {"mapbox-gl": {"optional": true}, "maplibre-gl": {"optional": true}}, "devDependencies": {"@types/react": "^16.0.0", "jsdom": "^16.5.0", "mapbox-gl": "^2.14.0", "maplibre-gl": "^2.4.0", "ocular-dev-tools": "2.0.0-alpha.13", "pre-commit": "^1.2.2", "react": "^18.0.0", "react-dom": "^18.0.0", "react-test-renderer": "^18.0.0", "typescript": "^4.0.0"}, "pre-commit": ["test-fast"]}