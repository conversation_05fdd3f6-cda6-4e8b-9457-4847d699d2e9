{"name": "@types/mapbox-gl", "version": "3.4.1", "description": "TypeScript definitions for mapbox-gl", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mapbox-gl", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dobrud"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "macobo", "url": "https://github.com/macobo"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "d<PERSON><PERSON>-gokun", "url": "https://github.com/dmytro-gokun"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/LiamAttClarke"}, {"name": "<PERSON>", "githubUsername": "life777", "url": "https://github.com/life777"}, {"name": "<PERSON>", "githubUsername": "amxfonseca", "url": "https://github.com/amxfonseca"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "Nosfit", "url": "https://github.com/Nosfit"}, {"name": "<PERSON>", "githubUsername": "m<PERSON><PERSON>", "url": "https://github.com/mbullington"}, {"name": "<PERSON>", "githubUsername": "pascal<PERSON>v", "url": "https://github.com/pascaloliv"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mschilde"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mapbox-gl"}, "scripts": {}, "dependencies": {"@types/geojson": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "a51998e0ac1b70506ba6681710308727b789ca86e76051b0468f6da95dad8b61", "typeScriptVersion": "4.8"}