{"ast": null, "code": "const refProps = ['type', 'source', 'source-layer', 'minzoom', 'maxzoom', 'filter', 'layout'];\n// Prepare a map style object for diffing\n// If immutable - convert to plain object\n// Work around some issues in older styles that would fail Mapbox's diffing\nexport function normalizeStyle(style) {\n  if (!style) {\n    return null;\n  }\n  if (typeof style === 'string') {\n    return style;\n  }\n  if ('toJS' in style) {\n    style = style.toJS();\n  }\n  if (!style.layers) {\n    return style;\n  }\n  const layerIndex = {};\n  for (const layer of style.layers) {\n    layerIndex[layer.id] = layer;\n  }\n  const layers = style.layers.map(layer => {\n    let normalizedLayer = null;\n    if ('interactive' in layer) {\n      normalizedLayer = Object.assign({}, layer);\n      // Breaks style diffing :(\n      // @ts-ignore legacy field not typed\n      delete normalizedLayer.interactive;\n    }\n    // Style diffing doesn't work with refs so expand them out manually before diffing.\n    // @ts-ignore legacy field not typed\n    const layerRef = layerIndex[layer.ref];\n    if (layerRef) {\n      normalizedLayer = normalizedLayer || Object.assign({}, layer);\n      // @ts-ignore\n      delete normalizedLayer.ref;\n      // https://github.com/mapbox/mapbox-gl-js/blob/master/src/style-spec/deref.js\n      for (const propName of refProps) {\n        if (propName in layerRef) {\n          normalizedLayer[propName] = layerRef[propName];\n        }\n      }\n    }\n    return normalizedLayer || layer;\n  });\n  // Do not mutate the style object provided by the user\n  return {\n    ...style,\n    layers\n  };\n}", "map": {"version": 3, "names": ["refProps", "normalizeStyle", "style", "toJS", "layers", "layerIndex", "layer", "id", "map", "normalizedLayer", "Object", "assign", "interactive", "layerRef", "ref", "propName"], "sources": ["C:\\Users\\<USER>\\Downloads\\morocco-complete-map-geojson-topojson-main\\morocco-vector-tiles\\node_modules\\react-map-gl\\src\\utils\\style-utils.ts"], "sourcesContent": ["import {ImmutableLike, MapStyle} from '../types';\n\nconst refProps = ['type', 'source', 'source-layer', 'minzoom', 'maxzoom', 'filter', 'layout'];\n\n// Prepare a map style object for diffing\n// If immutable - convert to plain object\n// Work around some issues in older styles that would fail Mapbox's diffing\nexport function normalizeStyle(\n  style: string | MapStyle | ImmutableLike<MapStyle>\n): string | MapStyle {\n  if (!style) {\n    return null;\n  }\n  if (typeof style === 'string') {\n    return style;\n  }\n  if ('toJS' in style) {\n    style = style.toJS();\n  }\n  if (!style.layers) {\n    return style;\n  }\n  const layerIndex = {};\n\n  for (const layer of style.layers) {\n    layerIndex[layer.id] = layer;\n  }\n\n  const layers = style.layers.map(layer => {\n    let normalizedLayer: typeof layer = null;\n\n    if ('interactive' in layer) {\n      normalizedLayer = Object.assign({}, layer);\n      // Breaks style diffing :(\n      // @ts-ignore legacy field not typed\n      delete normalizedLayer.interactive;\n    }\n\n    // Style diffing doesn't work with refs so expand them out manually before diffing.\n    // @ts-ignore legacy field not typed\n    const layerRef = layerIndex[layer.ref];\n    if (layerRef) {\n      normalizedLayer = normalizedLayer || Object.assign({}, layer);\n      // @ts-ignore\n      delete normalizedLayer.ref;\n      // https://github.com/mapbox/mapbox-gl-js/blob/master/src/style-spec/deref.js\n      for (const propName of refProps) {\n        if (propName in layerRef) {\n          normalizedLayer[propName] = layerRef[propName];\n        }\n      }\n    }\n\n    return normalizedLayer || layer;\n  });\n\n  // Do not mutate the style object provided by the user\n  return {...style, layers};\n}\n"], "mappings": "AAEA,MAAMA,QAAQ,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAE7F;AACA;AACA;AACA,OAAM,SAAUC,cAAcA,CAC5BC,KAAkD;EAElD,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;;EAEb,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;;EAEd,IAAI,MAAM,IAAIA,KAAK,EAAE;IACnBA,KAAK,GAAGA,KAAK,CAACC,IAAI,EAAE;;EAEtB,IAAI,CAACD,KAAK,CAACE,MAAM,EAAE;IACjB,OAAOF,KAAK;;EAEd,MAAMG,UAAU,GAAG,EAAE;EAErB,KAAK,MAAMC,KAAK,IAAIJ,KAAK,CAACE,MAAM,EAAE;IAChCC,UAAU,CAACC,KAAK,CAACC,EAAE,CAAC,GAAGD,KAAK;;EAG9B,MAAMF,MAAM,GAAGF,KAAK,CAACE,MAAM,CAACI,GAAG,CAACF,KAAK,IAAG;IACtC,IAAIG,eAAe,GAAiB,IAAI;IAExC,IAAI,aAAa,IAAIH,KAAK,EAAE;MAC1BG,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEL,KAAK,CAAC;MAC1C;MACA;MACA,OAAOG,eAAe,CAACG,WAAW;;IAGpC;IACA;IACA,MAAMC,QAAQ,GAAGR,UAAU,CAACC,KAAK,CAACQ,GAAG,CAAC;IACtC,IAAID,QAAQ,EAAE;MACZJ,eAAe,GAAGA,eAAe,IAAIC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEL,KAAK,CAAC;MAC7D;MACA,OAAOG,eAAe,CAACK,GAAG;MAC1B;MACA,KAAK,MAAMC,QAAQ,IAAIf,QAAQ,EAAE;QAC/B,IAAIe,QAAQ,IAAIF,QAAQ,EAAE;UACxBJ,eAAe,CAACM,QAAQ,CAAC,GAAGF,QAAQ,CAACE,QAAQ,CAAC;;;;IAKpD,OAAON,eAAe,IAAIH,KAAK;EACjC,CAAC,CAAC;EAEF;EACA,OAAO;IAAC,GAAGJ,KAAK;IAAEE;EAAM,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}