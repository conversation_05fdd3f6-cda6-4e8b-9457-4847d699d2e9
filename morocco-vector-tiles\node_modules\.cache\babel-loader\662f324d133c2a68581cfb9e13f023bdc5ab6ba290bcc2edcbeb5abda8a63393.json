{"ast": null, "code": "const globalSettings = ['baseApiUrl', 'maxParallelImageRequests', 'workerClass', 'workerCount', 'workerUrl'];\nexport default function setGlobals(mapLib, props) {\n  for (const key of globalSettings) {\n    if (key in props) {\n      mapLib[key] = props[key];\n    }\n  }\n  const {\n    RTLTextPlugin = 'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-rtl-text/v0.2.3/mapbox-gl-rtl-text.js'\n  } = props;\n  if (RTLTextPlugin && mapLib.getRTLTextPluginStatus && mapLib.getRTLTextPluginStatus() === 'unavailable') {\n    mapLib.setRTLTextPlugin(RTLTextPlugin, error => {\n      if (error) {\n        // eslint-disable-next-line\n        console.error(error);\n      }\n    }, true);\n  }\n}", "map": {"version": 3, "names": ["globalSettings", "setGlobals", "mapLib", "props", "key", "RTLTextPlugin", "getRTLTextPluginStatus", "setRTLTextPlugin", "error", "console"], "sources": ["C:\\Users\\<USER>\\Downloads\\morocco-complete-map-geojson-topojson-main\\morocco-vector-tiles\\node_modules\\react-map-gl\\src\\utils\\set-globals.ts"], "sourcesContent": ["export type GlobalSettings = {\n  /** The map's default API URL for requesting tiles, styles, sprites, and glyphs. */\n  baseApiUrl?: string;\n  /** The maximum number of images (raster tiles, sprites, icons) to load in parallel.\n   * @default 16\n   */\n  maxParallelImageRequests?: number;\n  /** The map's RTL text plugin. Necessary for supporting the Arabic and Hebrew languages, which are written right-to-left.  */\n  RTLTextPlugin?: string | false;\n  /** Provides an interface for external module bundlers such as Webpack or Rollup to package mapbox-gl's WebWorker into a separate class and integrate it with the library.\nTakes precedence over `workerUrl`. */\n  workerClass?: any;\n  /** The number of web workers instantiated on a page with mapbox-gl maps.\n   * @default 2\n   */\n  workerCount?: number;\n  /** Provides an interface for loading mapbox-gl's WebWorker bundle from a self-hosted URL.\n   * This is useful if your site needs to operate in a strict CSP (Content Security Policy) environment\n   * wherein you are not allowed to load JavaScript code from a Blob URL, which is default behavior. */\n  workerUrl?: string;\n};\n\nconst globalSettings = [\n  'baseApiUrl',\n  'maxParallelImageRequests',\n  'workerClass',\n  'workerCount',\n  'workerUrl'\n] as const;\n\nexport default function setGlobals(mapLib: any, props: GlobalSettings) {\n  for (const key of globalSettings) {\n    if (key in props) {\n      mapLib[key] = props[key];\n    }\n  }\n\n  const {\n    RTLTextPlugin = 'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-rtl-text/v0.2.3/mapbox-gl-rtl-text.js'\n  } = props;\n  if (\n    RTLTextPlugin &&\n    mapLib.getRTLTextPluginStatus &&\n    mapLib.getRTLTextPluginStatus() === 'unavailable'\n  ) {\n    mapLib.setRTLTextPlugin(\n      RTLTextPlugin,\n      (error?: Error) => {\n        if (error) {\n          // eslint-disable-next-line\n          console.error(error);\n        }\n      },\n      true\n    );\n  }\n}\n"], "mappings": "AAsBA,MAAMA,cAAc,GAAG,CACrB,YAAY,EACZ,0BAA0B,EAC1B,aAAa,EACb,aAAa,EACb,WAAW,CACH;AAEV,eAAc,SAAUC,UAAUA,CAACC,MAAW,EAAEC,KAAqB;EACnE,KAAK,MAAMC,GAAG,IAAIJ,cAAc,EAAE;IAChC,IAAII,GAAG,IAAID,KAAK,EAAE;MAChBD,MAAM,CAACE,GAAG,CAAC,GAAGD,KAAK,CAACC,GAAG,CAAC;;;EAI5B,MAAM;IACJC,aAAa,GAAG;EAA6F,CAC9G,GAAGF,KAAK;EACT,IACEE,aAAa,IACbH,MAAM,CAACI,sBAAsB,IAC7BJ,MAAM,CAACI,sBAAsB,EAAE,KAAK,aAAa,EACjD;IACAJ,MAAM,CAACK,gBAAgB,CACrBF,aAAa,EACZG,KAAa,IAAI;MAChB,IAAIA,KAAK,EAAE;QACT;QACAC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;;IAExB,CAAC,EACD,IAAI,CACL;;AAEL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}