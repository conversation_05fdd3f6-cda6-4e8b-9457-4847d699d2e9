{"ast": null, "code": "import { useEffect, useRef, memo } from 'react';\nimport { applyReactStyle } from '../utils/apply-react-style';\nimport useControl from './use-control';\nfunction ScaleControl(props) {\n  const ctrl = useControl(({\n    mapLib\n  }) => new mapLib.ScaleControl(props), {\n    position: props.position\n  });\n  const propsRef = useRef(props);\n  const prevProps = propsRef.current;\n  propsRef.current = props;\n  const {\n    style\n  } = props;\n  if (props.maxWidth !== undefined && props.maxWidth !== prevProps.maxWidth) {\n    ctrl.options.maxWidth = props.maxWidth;\n  }\n  if (props.unit !== undefined && props.unit !== prevProps.unit) {\n    ctrl.setUnit(props.unit);\n  }\n  useEffect(() => {\n    applyReactStyle(ctrl._container, style);\n  }, [style]);\n  return null;\n}\nexport default memo(ScaleControl);", "map": {"version": 3, "names": ["useEffect", "useRef", "memo", "applyReactStyle", "useControl", "ScaleControl", "props", "ctrl", "mapLib", "position", "propsRef", "prevProps", "current", "style", "max<PERSON><PERSON><PERSON>", "undefined", "options", "unit", "setUnit", "_container"], "sources": ["C:/Users/<USER>/Downloads/morocco-complete-map-geo<PERSON><PERSON>-topojson-main/morocco-vector-tiles/node_modules/react-map-gl/dist/esm/components/scale-control.js"], "sourcesContent": ["import { useEffect, useRef, memo } from 'react';\nimport { applyReactStyle } from '../utils/apply-react-style';\nimport useControl from './use-control';\nfunction ScaleControl(props) {\n    const ctrl = useControl(({ mapLib }) => new mapLib.ScaleControl(props), {\n        position: props.position\n    });\n    const propsRef = useRef(props);\n    const prevProps = propsRef.current;\n    propsRef.current = props;\n    const { style } = props;\n    if (props.maxWidth !== undefined && props.maxWidth !== prevProps.maxWidth) {\n        ctrl.options.maxWidth = props.maxWidth;\n    }\n    if (props.unit !== undefined && props.unit !== prevProps.unit) {\n        ctrl.setUnit(props.unit);\n    }\n    useEffect(() => {\n        applyReactStyle(ctrl._container, style);\n    }, [style]);\n    return null;\n}\nexport default memo(ScaleControl);\n//# sourceMappingURL=scale-control.js.map"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,IAAI,QAAQ,OAAO;AAC/C,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,YAAYA,CAACC,KAAK,EAAE;EACzB,MAAMC,IAAI,GAAGH,UAAU,CAAC,CAAC;IAAEI;EAAO,CAAC,KAAK,IAAIA,MAAM,CAACH,YAAY,CAACC,KAAK,CAAC,EAAE;IACpEG,QAAQ,EAAEH,KAAK,CAACG;EACpB,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGT,MAAM,CAACK,KAAK,CAAC;EAC9B,MAAMK,SAAS,GAAGD,QAAQ,CAACE,OAAO;EAClCF,QAAQ,CAACE,OAAO,GAAGN,KAAK;EACxB,MAAM;IAAEO;EAAM,CAAC,GAAGP,KAAK;EACvB,IAAIA,KAAK,CAACQ,QAAQ,KAAKC,SAAS,IAAIT,KAAK,CAACQ,QAAQ,KAAKH,SAAS,CAACG,QAAQ,EAAE;IACvEP,IAAI,CAACS,OAAO,CAACF,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;EAC1C;EACA,IAAIR,KAAK,CAACW,IAAI,KAAKF,SAAS,IAAIT,KAAK,CAACW,IAAI,KAAKN,SAAS,CAACM,IAAI,EAAE;IAC3DV,IAAI,CAACW,OAAO,CAACZ,KAAK,CAACW,IAAI,CAAC;EAC5B;EACAjB,SAAS,CAAC,MAAM;IACZG,eAAe,CAACI,IAAI,CAACY,UAAU,EAAEN,KAAK,CAAC;EAC3C,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,OAAO,IAAI;AACf;AACA,eAAeX,IAAI,CAACG,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}