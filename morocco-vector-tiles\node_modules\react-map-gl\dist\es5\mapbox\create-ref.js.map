{"version": 3, "file": "create-ref.js", "sourceRoot": "", "sources": ["../../../src/mapbox/create-ref.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAUA,mEAAmE;AACnE,IAAM,WAAW,GAAG;IAClB,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,aAAa;IACb,sBAAsB;IACtB,eAAe;IACf,UAAU;IACV,WAAW;IACX,cAAc;IACd,UAAU;IACV,aAAa;IACb,mBAAmB;IACnB,WAAW;IACX,kBAAkB;IAClB,mBAAmB;IACnB,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,QAAQ;CACA,CAAC;AAMX,SAAwB,SAAS,CAI/B,WAA6C;;IAC7C,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,IAAI,CAAC;KACb;IAED,IAAM,GAAG,GAAG,WAAW,CAAC,GAAgC,CAAC;IACzD,IAAM,MAAM,GAAQ;QAClB,MAAM,EAAE,cAAM,OAAA,GAAG,EAAH,CAAG;QAEjB,gDAAgD;QAChD,SAAS,EAAE,cAAM,OAAA,WAAW,CAAC,SAAS,CAAC,MAAM,EAA5B,CAA4B;QAC7C,OAAO,EAAE,cAAM,OAAA,WAAW,CAAC,SAAS,CAAC,IAAI,EAA1B,CAA0B;QACzC,UAAU,EAAE,cAAM,OAAA,WAAW,CAAC,SAAS,CAAC,OAAO,EAA7B,CAA6B;QAC/C,QAAQ,EAAE,cAAM,OAAA,WAAW,CAAC,SAAS,CAAC,KAAK,EAA3B,CAA2B;QAC3C,UAAU,EAAE,cAAM,OAAA,WAAW,CAAC,SAAS,CAAC,OAAO,EAA7B,CAA6B;QAC/C,SAAS,EAAE,cAAM,OAAA,WAAW,CAAC,SAAS,CAAC,SAAS,EAAE,EAAjC,CAAiC;QAClD,OAAO,EAAE,UAAC,MAAkB;YAC1B,IAAM,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC;YACzB,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;YACtC,IAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACnC,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,SAAS,EAAE,UAAC,KAAgB;YAC1B,IAAM,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC;YACzB,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;YACtC,IAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACpC,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,8CAA8C;QAC9C,qBAAqB,EAAE,UAAC,MAAkB,EAAE,OAAa;YACvD,IAAM,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC;YACzB,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;YACtC,IAAM,MAAM,GAAG,GAAG,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC1D,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,qBAAqB,EAAE,UAAC,QAAc,EAAE,OAAa;YACnD,IAAM,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC;YACzB,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;YACtC,IAAM,MAAM,GAAG,GAAG,CAAC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC5D,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;KACF,CAAC;;QAEF,KAAkB,IAAA,KAAA,SAAA,cAAc,CAAC,GAAG,CAAC,CAAA,gBAAA,4BAAE;YAAlC,IAAM,GAAG,WAAA;YACZ,mBAAmB;YACnB,IAAI,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAClD,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClC;SACF;;;;;;;;;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AA3DD,4BA2DC;AAED,SAAS,cAAc,CAAC,GAAW;;IACjC,IAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;IAEjC,IAAI,KAAK,GAAG,GAAG,CAAC;IAChB,OAAO,KAAK,EAAE;;YACZ,KAAkB,IAAA,oBAAA,SAAA,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA,CAAA,gBAAA,4BAAE;gBAAhD,IAAM,GAAG,WAAA;gBACZ,IACE,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG;oBACd,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,UAAU;oBAC9B,GAAG,KAAK,MAAM;oBACd,GAAG,KAAK,kBAAkB,EAC1B;oBACA,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iBACjB;aACF;;;;;;;;;QACD,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;KACtC;IACD,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5B,CAAC"}