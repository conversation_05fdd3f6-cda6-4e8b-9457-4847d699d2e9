{"ast": null, "code": "import { useContext, useEffect, useMemo, useState, useRef } from 'react';\nimport { MapContext } from './map';\nimport assert from '../utils/assert';\nimport { deepEqual } from '../utils/deep-equal';\n/* eslint-disable complexity, max-statements */\nfunction updateLayer(map, id, props, prevProps) {\n  assert(props.id === prevProps.id, 'layer id changed');\n  assert(props.type === prevProps.type, 'layer type changed');\n  if (props.type === 'custom' || prevProps.type === 'custom') {\n    return;\n  }\n  const {\n    layout = {},\n    paint = {},\n    filter,\n    minzoom,\n    maxzoom,\n    beforeId\n  } = props;\n  if (beforeId !== prevProps.beforeId) {\n    map.moveLayer(id, beforeId);\n  }\n  if (layout !== prevProps.layout) {\n    const prevLayout = prevProps.layout || {};\n    for (const key in layout) {\n      if (!deepEqual(layout[key], prevLayout[key])) {\n        map.setLayoutProperty(id, key, layout[key]);\n      }\n    }\n    for (const key in prevLayout) {\n      if (!layout.hasOwnProperty(key)) {\n        map.setLayoutProperty(id, key, undefined);\n      }\n    }\n  }\n  if (paint !== prevProps.paint) {\n    const prevPaint = prevProps.paint || {};\n    for (const key in paint) {\n      if (!deepEqual(paint[key], prevPaint[key])) {\n        map.setPaintProperty(id, key, paint[key]);\n      }\n    }\n    for (const key in prevPaint) {\n      if (!paint.hasOwnProperty(key)) {\n        map.setPaintProperty(id, key, undefined);\n      }\n    }\n  }\n  if (!deepEqual(filter, prevProps.filter)) {\n    map.setFilter(id, filter);\n  }\n  if (minzoom !== prevProps.minzoom || maxzoom !== prevProps.maxzoom) {\n    map.setLayerZoomRange(id, minzoom, maxzoom);\n  }\n}\nfunction createLayer(map, id, props) {\n  // @ts-ignore\n  if (map.style && map.style._loaded && (!('source' in props) || map.getSource(props.source))) {\n    const options = {\n      ...props,\n      id\n    };\n    delete options.beforeId;\n    // @ts-ignore\n    map.addLayer(options, props.beforeId);\n  }\n}\n/* eslint-enable complexity, max-statements */\nlet layerCounter = 0;\nfunction Layer(props) {\n  const map = useContext(MapContext).map.getMap();\n  const propsRef = useRef(props);\n  const [, setStyleLoaded] = useState(0);\n  const id = useMemo(() => props.id || `jsx-layer-${layerCounter++}`, []);\n  useEffect(() => {\n    if (map) {\n      const forceUpdate = () => setStyleLoaded(version => version + 1);\n      map.on('styledata', forceUpdate);\n      forceUpdate();\n      return () => {\n        map.off('styledata', forceUpdate);\n        // @ts-ignore\n        if (map.style && map.style._loaded && map.getLayer(id)) {\n          map.removeLayer(id);\n        }\n      };\n    }\n    return undefined;\n  }, [map]);\n  // @ts-ignore\n  const layer = map && map.style && map.getLayer(id);\n  if (layer) {\n    try {\n      updateLayer(map, id, props, propsRef.current);\n    } catch (error) {\n      console.warn(error); // eslint-disable-line\n    }\n  } else {\n    createLayer(map, id, props);\n  }\n  // Store last rendered props\n  propsRef.current = props;\n  return null;\n}\nexport default Layer;", "map": {"version": 3, "names": ["useContext", "useEffect", "useMemo", "useState", "useRef", "MapContext", "assert", "deepEqual", "updateLayer", "map", "id", "props", "prevProps", "type", "layout", "paint", "filter", "minzoom", "maxzoom", "beforeId", "<PERSON><PERSON><PERSON><PERSON>", "prevLayout", "key", "setLayoutProperty", "hasOwnProperty", "undefined", "prev<PERSON><PERSON><PERSON>", "setPaintProperty", "setFilter", "setLayerZoomRange", "create<PERSON><PERSON>er", "style", "_loaded", "getSource", "source", "options", "add<PERSON><PERSON>er", "layerCounter", "Layer", "getMap", "propsRef", "setStyleLoaded", "forceUpdate", "version", "on", "off", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "layer", "current", "error", "console", "warn"], "sources": ["../../../src/components/layer.ts"], "sourcesContent": [null], "mappings": "AAAA,SAAQA,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,QAAO,OAAO;AACtE,SAAQC,UAAU,QAAO,OAAO;AAChC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,SAAQC,SAAS,QAAO,qBAAqB;AAc7C;AACA,SAASC,WAAWA,CAClBC,GAAgB,EAChBC,EAAU,EACVC,KAAyB,EACzBC,SAA6B;EAE7BN,MAAM,CAACK,KAAK,CAACD,EAAE,KAAKE,SAAS,CAACF,EAAE,EAAE,kBAAkB,CAAC;EACrDJ,MAAM,CAACK,KAAK,CAACE,IAAI,KAAKD,SAAS,CAACC,IAAI,EAAE,oBAAoB,CAAC;EAE3D,IAAIF,KAAK,CAACE,IAAI,KAAK,QAAQ,IAAID,SAAS,CAACC,IAAI,KAAK,QAAQ,EAAE;IAC1D;;EAGF,MAAM;IAACC,MAAM,GAAG,EAAE;IAAEC,KAAK,GAAG,EAAE;IAAEC,MAAM;IAAEC,OAAO;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGR,KAAK;EAE3E,IAAIQ,QAAQ,KAAKP,SAAS,CAACO,QAAQ,EAAE;IACnCV,GAAG,CAACW,SAAS,CAACV,EAAE,EAAES,QAAQ,CAAC;;EAE7B,IAAIL,MAAM,KAAKF,SAAS,CAACE,MAAM,EAAE;IAC/B,MAAMO,UAAU,GAAGT,SAAS,CAACE,MAAM,IAAI,EAAE;IACzC,KAAK,MAAMQ,GAAG,IAAIR,MAAM,EAAE;MACxB,IAAI,CAACP,SAAS,CAACO,MAAM,CAACQ,GAAG,CAAC,EAAED,UAAU,CAACC,GAAG,CAAC,CAAC,EAAE;QAC5Cb,GAAG,CAACc,iBAAiB,CAACb,EAAE,EAAEY,GAAG,EAAER,MAAM,CAACQ,GAAG,CAAC,CAAC;;;IAG/C,KAAK,MAAMA,GAAG,IAAID,UAAU,EAAE;MAC5B,IAAI,CAACP,MAAM,CAACU,cAAc,CAACF,GAAG,CAAC,EAAE;QAC/Bb,GAAG,CAACc,iBAAiB,CAACb,EAAE,EAAEY,GAAG,EAAEG,SAAS,CAAC;;;;EAI/C,IAAIV,KAAK,KAAKH,SAAS,CAACG,KAAK,EAAE;IAC7B,MAAMW,SAAS,GAAGd,SAAS,CAACG,KAAK,IAAI,EAAE;IACvC,KAAK,MAAMO,GAAG,IAAIP,KAAK,EAAE;MACvB,IAAI,CAACR,SAAS,CAACQ,KAAK,CAACO,GAAG,CAAC,EAAEI,SAAS,CAACJ,GAAG,CAAC,CAAC,EAAE;QAC1Cb,GAAG,CAACkB,gBAAgB,CAACjB,EAAE,EAAEY,GAAG,EAAEP,KAAK,CAACO,GAAG,CAAC,CAAC;;;IAG7C,KAAK,MAAMA,GAAG,IAAII,SAAS,EAAE;MAC3B,IAAI,CAACX,KAAK,CAACS,cAAc,CAACF,GAAG,CAAC,EAAE;QAC9Bb,GAAG,CAACkB,gBAAgB,CAACjB,EAAE,EAAEY,GAAG,EAAEG,SAAS,CAAC;;;;EAK9C,IAAI,CAAClB,SAAS,CAACS,MAAM,EAAEJ,SAAS,CAACI,MAAM,CAAC,EAAE;IACxCP,GAAG,CAACmB,SAAS,CAAClB,EAAE,EAAEM,MAAM,CAAC;;EAE3B,IAAIC,OAAO,KAAKL,SAAS,CAACK,OAAO,IAAIC,OAAO,KAAKN,SAAS,CAACM,OAAO,EAAE;IAClET,GAAG,CAACoB,iBAAiB,CAACnB,EAAE,EAAEO,OAAO,EAAEC,OAAO,CAAC;;AAE/C;AAEA,SAASY,WAAWA,CAClBrB,GAAgB,EAChBC,EAAU,EACVC,KAAyB;EAEzB;EACA,IAAIF,GAAG,CAACsB,KAAK,IAAItB,GAAG,CAACsB,KAAK,CAACC,OAAO,KAAK,EAAE,QAAQ,IAAIrB,KAAK,CAAC,IAAIF,GAAG,CAACwB,SAAS,CAACtB,KAAK,CAACuB,MAAM,CAAC,CAAC,EAAE;IAC3F,MAAMC,OAAO,GAAuB;MAAC,GAAGxB,KAAK;MAAED;IAAE,CAAC;IAClD,OAAOyB,OAAO,CAAChB,QAAQ;IAEvB;IACAV,GAAG,CAAC2B,QAAQ,CAACD,OAAO,EAAExB,KAAK,CAACQ,QAAQ,CAAC;;AAEzC;AAEA;AAEA,IAAIkB,YAAY,GAAG,CAAC;AAEpB,SAASC,KAAKA,CAAwB3B,KAAgD;EACpF,MAAMF,GAAG,GAAGT,UAAU,CAACK,UAAU,CAAC,CAACI,GAAG,CAAC8B,MAAM,EAAE;EAC/C,MAAMC,QAAQ,GAAGpC,MAAM,CAACO,KAAK,CAAC;EAC9B,MAAM,GAAG8B,cAAc,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EAEtC,MAAMO,EAAE,GAAGR,OAAO,CAAC,MAAMS,KAAK,CAACD,EAAE,IAAI,aAAa2B,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC;EAEvEpC,SAAS,CAAC,MAAK;IACb,IAAIQ,GAAG,EAAE;MACP,MAAMiC,WAAW,GAAGA,CAAA,KAAMD,cAAc,CAACE,OAAO,IAAIA,OAAO,GAAG,CAAC,CAAC;MAChElC,GAAG,CAACmC,EAAE,CAAC,WAAW,EAAEF,WAAW,CAAC;MAChCA,WAAW,EAAE;MAEb,OAAO,MAAK;QACVjC,GAAG,CAACoC,GAAG,CAAC,WAAW,EAAEH,WAAW,CAAC;QACjC;QACA,IAAIjC,GAAG,CAACsB,KAAK,IAAItB,GAAG,CAACsB,KAAK,CAACC,OAAO,IAAIvB,GAAG,CAACqC,QAAQ,CAACpC,EAAE,CAAC,EAAE;UACtDD,GAAG,CAACsC,WAAW,CAACrC,EAAE,CAAC;;MAEvB,CAAC;;IAEH,OAAOe,SAAS;EAClB,CAAC,EAAE,CAAChB,GAAG,CAAC,CAAC;EAET;EACA,MAAMuC,KAAK,GAAGvC,GAAG,IAAIA,GAAG,CAACsB,KAAK,IAAItB,GAAG,CAACqC,QAAQ,CAACpC,EAAE,CAAC;EAClD,IAAIsC,KAAK,EAAE;IACT,IAAI;MACFxC,WAAW,CAACC,GAAG,EAAEC,EAAE,EAAEC,KAAK,EAAE6B,QAAQ,CAACS,OAAO,CAAC;KAC9C,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC;;GAExB,MAAM;IACLpB,WAAW,CAACrB,GAAG,EAAEC,EAAE,EAAEC,KAAK,CAAC;;EAG7B;EACA6B,QAAQ,CAACS,OAAO,GAAGtC,KAAK;EAExB,OAAO,IAAI;AACb;AAEA,eAAe2B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}