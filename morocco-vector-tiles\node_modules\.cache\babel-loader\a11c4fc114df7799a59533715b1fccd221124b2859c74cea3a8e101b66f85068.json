{"ast": null, "code": "// This is a simplified version of\n// https://github.com/facebook/react/blob/4131af3e4bf52f3a003537ec95a1655147c81270/src/renderers/dom/shared/CSSPropertyOperations.js#L62\nconst unitlessNumber = /box|flex|grid|column|lineHeight|fontWeight|opacity|order|tabSize|zIndex/;\nexport function applyReactStyle(element, styles) {\n  if (!element || !styles) {\n    return;\n  }\n  const style = element.style;\n  for (const key in styles) {\n    const value = styles[key];\n    if (Number.isFinite(value) && !unitlessNumber.test(key)) {\n      style[key] = `${value}px`;\n    } else {\n      style[key] = value;\n    }\n  }\n}", "map": {"version": 3, "names": ["unitlessNumber", "applyReactStyle", "element", "styles", "style", "key", "value", "Number", "isFinite", "test"], "sources": ["C:\\Users\\<USER>\\Downloads\\morocco-complete-map-geojson-topojson-main\\morocco-vector-tiles\\node_modules\\react-map-gl\\src\\utils\\apply-react-style.ts"], "sourcesContent": ["import * as React from 'react';\n// This is a simplified version of\n// https://github.com/facebook/react/blob/4131af3e4bf52f3a003537ec95a1655147c81270/src/renderers/dom/shared/CSSPropertyOperations.js#L62\nconst unitlessNumber = /box|flex|grid|column|lineHeight|fontWeight|opacity|order|tabSize|zIndex/;\n\nexport function applyReactStyle(element: HTMLElement, styles: React.CSSProperties) {\n  if (!element || !styles) {\n    return;\n  }\n  const style = element.style;\n\n  for (const key in styles) {\n    const value = styles[key];\n    if (Number.isFinite(value) && !unitlessNumber.test(key)) {\n      style[key] = `${value}px`;\n    } else {\n      style[key] = value;\n    }\n  }\n}\n"], "mappings": "AACA;AACA;AACA,MAAMA,cAAc,GAAG,yEAAyE;AAEhG,OAAM,SAAUC,eAAeA,CAACC,OAAoB,EAAEC,MAA2B;EAC/E,IAAI,CAACD,OAAO,IAAI,CAACC,MAAM,EAAE;IACvB;;EAEF,MAAMC,KAAK,GAAGF,OAAO,CAACE,KAAK;EAE3B,KAAK,MAAMC,GAAG,IAAIF,MAAM,EAAE;IACxB,MAAMG,KAAK,GAAGH,MAAM,CAACE,GAAG,CAAC;IACzB,IAAIE,MAAM,CAACC,QAAQ,CAACF,KAAK,CAAC,IAAI,CAACN,cAAc,CAACS,IAAI,CAACJ,GAAG,CAAC,EAAE;MACvDD,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGC,KAAK,IAAI;KAC1B,MAAM;MACLF,KAAK,CAACC,GAAG,CAAC,GAAGC,KAAK;;;AAGxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}