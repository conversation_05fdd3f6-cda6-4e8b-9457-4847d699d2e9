{"type": "Topology", "arcs": [[[129, 1024], [1, 39], [59, 231], [6, 69], [-5, 59], [3, 36], [24, 43], [5, 18], [2, 19], [-1, 18], [-4, 9], [-19, 16], [-5, 9], [8, 23], [6, 46], [27, 65], [11, 64], [13, 37], [148, 312], [51, 55], [7, 15], [3, 9], [16, 38], [8, 8], [23, 17], [8, 9], [23, 54], [16, 22], [34, 14], [8, 8], [10, 8], [16, 1], [12, -5], [11, -9], [15, -19], [156, 64], [28, 24], [28, 35], [22, 39], [50, 181], [3, 28], [14, 17], [70, 73], [18, 24], [18, 100], [4, 133], [7, 34], [17, 56], [12, 26], [28, 41], [0, 16], [-5, 12], [-3, 10], [0, 19], [2, 10], [13, 29], [3, 10], [6, 6], [32, 3], [34, 9], [45, 42], [48, 55], [13, 31], [10, 32], [6, 34], [1, 38], [-11, 36], [-28, 6], [-58, -16], [10, 29], [24, 50], [13, 18], [67, 64], [6, 10], [106, 250], [16, 21], [15, 9], [18, 18], [63, 111], [20, 105], [15, 29], [22, 31], [50, 93], [69, 80], [4, 10], [24, 18], [10, 10], [7, 17], [7, 32], [6, 16], [22, 36], [22, 29], [17, 30], [7, 39], [3, 69], [-5, 29], [-18, 36], [5, 8], [5, 13], [-17, -12], [-1, -19], [2, -21], [-3, -23], [-10, -11], [-36, -26], [-13, -6], [-19, -1], [-12, 4], [-14, -3], [-22, -21], [-15, -19], [-10, -19], [-22, -53], [-18, -34], [-4, -10], [-3, -18], [-13, -32], [-3, -14], [-12, -18], [-66, -62], [-21, 16], [9, 26], [31, 38], [8, 23], [51, 100], [88, 124], [9, 24], [20, 28], [23, 25], [103, 89], [46, 18], [61, 39], [18, 18], [47, 5], [37, 24], [30, 35], [22, 34], [58, 69], [40, 60], [54, 40], [23, 22], [10, 29], [11, 18], [130, 109], [78, 84], [97, 150], [36, 42], [46, 42], [26, 18], [28, 14], [29, 9], [62, 11], [29, 17], [48, 38], [-1, 36], [5, 9], [17, 36], [34, 43], [34, 28], [35, 19], [27, 22], [18, 31], [13, 102], [34, 93], [12, 73], [20, 45], [5, 21], [-3, 26], [-6, 24], [-1, 23], [10, 25], [-11, 10], [9, 22], [7, 25], [4, 26], [1, 25], [-4, 25], [-14, 34], [-3, 20], [2, 139], [-10, 39], [43, 182], [10, 25], [42, 158], [65, 92], [19, 16], [11, 15], [42, 103], [10, 70], [3, 10], [13, 28], [8, 49], [57, 145], [106, 150], [38, 69], [8, 21], [4, 31], [23, 70], [12, 22], [-6, 25], [14, 89], [-4, 23], [-13, 47], [4, 16], [11, 17], [15, 46], [8, 17], [79, 104], [13, 9], [67, 15], [77, 42], [118, 114], [19, 27], [13, 27], [18, 23], [33, 15], [156, 16], [27, 13], [147, 76], [257, 184], [173, 111], [83, 94], [86, 171], [29, 84], [27, 127], [41, 91], [42, 198], [27, 60], [116, 171], [1, 9], [-10, 14], [0, 9], [19, 11], [76, 178], [47, 160], [3, 33], [6, 28], [29, 60], [3, 28], [0, 0], [1, 7], [9, 28], [15, 15], [43, 21], [46, 38], [52, 17], [23, 26], [59, 96], [34, 114], [16, 33], [22, 22], [27, 16], [134, 49], [256, 44], [204, -5], [253, 82], [387, 65], [16, 11], [32, 33], [6, 4], [25, 6], [316, 136], [420, 181], [47, 36], [33, 43], [56, 92], [22, 24], [26, 22], [21, 24], [9, 32], [12, 24], [84, 57], [67, 80], [11, 22], [58, 36], [11, 12], [12, 22], [7, 10], [49, 38], [5, 6], [29, 48], [39, 41], [22, 14], [403, 202], [97, 63], [116, 58], [55, 36], [174, 159], [145, 215], [39, 45], [26, 22], [54, 19], [22, 24], [34, 53], [36, 44], [16, 28], [7, 29], [7, 13], [51, 36], [7, 10], [12, 33], [59, 84], [8, 18], [3, 33], [9, 35], [13, 32], [15, 23], [67, 61], [11, 19], [9, 25], [59, 77], [183, 193], [145, 247], [89, 204], [14, 58], [24, 200], [26, 104], [4, 31], [-7, 42], [-15, 20], [-22, 15], [-24, 24], [-16, 27], [-36, 79], [-12, 52], [-11, 7], [-16, 2], [-16, 6], [-13, 12], [-26, 42], [-101, 65], [-30, 2], [-33, 7], [-20, 22], [2, 60], [30, 59], [38, 56], [26, 51], [9, 35], [2, 20], [-6, 15], [-10, 14], [-1, 10], [4, 11], [2, 18], [-10, 308], [-6, 28], [-22, 57], [17, 36], [37, 222], [4, 90], [-7, 37], [-15, 19], [-18, 15], [-18, 21], [44, 54], [33, 54], [33, 95], [16, 22], [26, 22], [9, 12], [8, 28], [20, 20], [9, 18], [19, 25], [7, 15], [20, 126], [11, 24], [129, 134], [57, 75], [11, 11], [21, 15], [11, 12], [11, 15], [13, 27], [6, 11], [167, 163], [17, 32], [11, 23], [16, 63], [3, 26], [7, 19], [51, 61], [20, 43], [11, 48], [7, 162], [-5, 8], [-27, 12], [-6, 12], [3, 34], [9, 24], [39, 70], [10, 31], [3, 31], [-11, 25], [-13, 16], [-12, 23], [-9, 26], [-1, 26], [27, 48], [209, 166], [161, 179], [137, 91], [48, 42], [160, 202], [178, 183], [11, 19], [2, 19], [-1, 34], [13, 20], [117, 129], [11, 6], [12, -3], [14, -14], [12, -3], [35, 2], [25, 5], [52, 25], [51, 39], [83, 95], [51, 38], [58, 23], [194, 41], [134, 56], [70, 29], [65, 15], [35, 14], [31, 36], [35, 15], [95, 27], [56, 28], [27, 5], [26, 14], [23, 26], [24, 20], [28, -6], [16, 9], [14, -2], [13, -6], [16, -1], [13, 5], [25, 14], [42, 8], [29, 11], [27, 14], [18, 13], [48, 57], [41, 19], [40, 45], [27, 8], [-10, -11], [29, -9], [30, 8], [16, 10], [104, 65], [54, 45], [52, 26], [57, 19], [50, 8], [1, 0], [24, 8], [45, 37], [51, 16], [148, 110], [149, 150], [125, 192], [93, 185], [156, 215], [14, 27], [24, 61], [32, 49], [306, 589], [123, 382], [257, 590], [7, 30], [3, 38], [6, 23], [30, 62], [30, 85], [2, 13], [5, 14], [18, 24], [4, 16], [9, 73], [10, 37], [12, 24], [28, 26], [49, 7], [130, -18], [17, 3], [38, 35], [18, 12], [25, 13], [26, 5], [18, -8], [25, 11], [33, 1], [34, -8], [24, -14], [83, 45], [24, 19], [51, 53], [26, 21], [29, 12], [12, 0], [6, -6], [17, -9], [13, 1], [6, 12], [3, 12], [4, 3], [9, -3], [0, 0], [13, -35], [16, -32], [22, -28], [31, -26], [0, 0], [-7, -23], [5, -39], [21, -82], [7, -65], [6, -19], [8, -15], [11, -11], [9, -2], [24, 4], [7, -2], [2, -9], [-3, -24], [1, -10], [24, -83], [15, -32], [29, -25], [60, -39], [50, -43], [120, -155], [22, -15], [34, -6], [26, -7], [28, -18], [48, -40], [40, -50], [24, -25], [63, -20], [77, -66], [57, -29], [201, -56], [188, -39], [2, 0], [167, -45], [40, -3], [29, 7], [82, 48], [196, 31], [119, 49], [121, 22], [59, 26], [14, -18], [-4, -7], [-17, -5], [-7, -6], [6, -9], [10, -11], [20, -26], [21, -10], [29, -1], [39, -3], [27, 2], [49, 4], [32, 14], [13, 28], [12, 43], [30, 29], [37, 14], [37, -5], [28, -17], [59, -50], [30, -18], [106, -16], [38, -19], [17, -1], [18, 2], [15, 0], [16, -5], [27, -12], [15, -5], [72, -5], [61, 11], [175, 57], [30, 17], [64, 62], [15, 9], [12, -1], [13, -6], [16, -5], [17, 2], [45, 42], [56, 124], [36, 48], [-3, 17], [7, 13], [13, 5], [17, -9], [6, -12], [-7, -27], [6, -19], [-14, -48], [19, -60], [2, -8], [0, 0], [-27, -25], [5, -44], [29, -28], [43, 14], [19, -27], [-12, -14], [-6, -24], [-2, -26], [5, -51], [7, -20], [12, -19], [21, -23], [36, -24], [50, -15], [103, -9], [-62, 45], [-108, 114], [-21, 33], [7, 12], [19, -14], [68, -67], [11, -17], [68, -64], [60, -31], [73, -24], [147, -18], [64, 5], [35, 7], [22, 13], [30, 31], [27, 20], [29, 7], [135, -48], [32, -4], [33, -10], [38, -20], [37, -12], [2, -62], [13, -41], [26, -32], [43, -14], [53, -35], [44, -38], [47, -28], [64, -6], [18, -12], [5, -19], [2, -21], [7, -22], [20, -21], [75, -43], [48, -41], [148, -86], [26, -25], [-5, -11], [-18, -13], [-35, -71], [-74, -105], [-12, -26], [171, -161], [51, -14], [16, -9], [-151, -168], [55, -59], [35, -69], [101, -289], [4, -22], [3, -20], [-3, -32], [-66, -252], [-5, -74], [13, -77], [15, -46], [-1, -17], [-11, -23], [-14, -17], [-17, -13], [-13, -16], [-7, -24], [9, -25], [22, -14], [49, -16], [24, -17], [16, -18], [64, -142], [6, -51], [-18, -43], [-21, -29], [-27, -88], [-19, -39], [-15, -40], [0, -154], [13, -52], [72, -65], [25, -44], [19, -50], [30, -38], [36, -32], [41, -28], [24, -21], [9, -33], [0, -36], [-4, -35], [-4, -16], [-6, -15], [-8, -13], [-11, -10], [-60, -41], [192, -300], [47, -37], [89, -31], [396, -285], [22, -35], [-82, -86], [-47, -34], [-53, -20], [-58, -8], [-23, -12], [-23, -28], [-14, -28], [-19, -56], [-23, -144], [-3, -18], [1, -13], [-2, -13], [-9, -17], [-9, -8], [-23, -18], [-6, -10], [6, -25], [23, -1], [53, 20], [27, 0], [29, -8], [23, -19], [8, -33], [-28, -56], [-56, -12], [-107, 5], [-12, 0], [-34, 2], [-53, 2], [-71, 3], [-86, 4], [-99, 4], [-108, 5], [-116, 5], [-121, 6], [-124, 5], [-125, 6], [-122, 5], [-118, 5], [-111, 5], [-101, 5], [-90, 4], [-195, 8], [-254, -67], [-262, -21], [-82, -43], [98, -245], [59, -153], [-247, -33], [-307, -88], [-414, -70], [-52, -4], [-61, 13], [-96, -48], [-20, -405], [-104, -7], [-78, -54], [-18, -21], [-6, -29], [9, -118], [-3, -36], [-30, -48], [-9, -31], [5, -27], [16, -14], [24, -1], [25, 10], [42, 37], [21, 11], [24, -6], [21, -21], [38, -59], [25, -23], [52, -23], [16, -15], [14, -29], [6, -28], [2, -30], [76, -118], [-79, -120], [-69, -66], [10, -98], [10, -99], [-267, -131], [-238, -55], [-217, -11], [-168, -44], [-139, -77], [-158, -197], [-198, -153], [-208, -88], [-148, -76], [-119, -88], [-158, -131], [-148, -99], [-139, -142], [-99, -187], [-128, -197], [-149, -186], [-138, -44], [-119, 44], [-50, 142], [-178, -22], [-168, -32], [-178, 0], [-208, 0], [-197, -22], [-208, -55], [-198, -22], [-119, -110], [-247, 99], [-158, 11], [-109, -11], [-158, -66], [-129, -131], [-161, 9], [-29, -11], [-31, -3], [-32, 1], [-63, 10], [-66, 3], [-48, -20], [-87, -86], [-21, -16], [-68, -35], [-87, -78], [-150, -99], [-129, -120], [-47, -32], [-159, -68], [-97, -64], [-94, -88], [-23, -13], [-50, -22], [-21, -17], [-49, -84], [-18, -17], [-64, -35], [-64, -50], [-181, -96], [-26, -22], [-16, -30], [-5, -42], [0, -71], [0, -94], [0, -94], [0, -94], [0, -94], [0, -94], [0, -94], [0, -94], [0, -94], [0, -94], [0, -93], [0, -94], [0, -94], [0, -94], [0, -94], [0, -94], [0, -94], [0, -147], [0, -148], [0, -147], [0, -147], [0, -22], [0, -61], [0, -95], [0, -123], [0, -145], [1, -163], [0, -174], [0, -179], [0, -179], [0, -174], [1, -162], [0, -146], [0, -123], [0, -95], [0, -61], [0, -22], [0, -70], [-10, -21], [-27, -8], [-120, 0], [-143, 0], [-143, 0], [-142, 0], [-143, 0], [-142, 0], [-143, 0], [-142, 0], [-143, 0], [-143, 0], [-142, 0], [-143, 0], [-142, 0], [-143, 0], [-143, 0], [-142, 0], [-143, 0], [-142, 0], [-143, 0], [-142, 0], [-143, 0], [-142, 0], [-143, 0], [-142, 0], [-143, 0], [-143, 0], [-142, 0], [-143, 0], [-142, 0], [-143, 0], [-142, 0], [-143, 0], [-143, 0], [0, -119], [0, -118], [0, -119], [0, -118], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -119], [0, -118], [0, -127], [-5, -53], [-19, -26], [-144, -40], [-311, -152], [-51, -15], [-238, -35], [-87, -31], [-89, -62], [-152, -108], [-177, -125], [-142, -101], [-27, -36], [-121, -175], [-46, -99], [-19, -106], [15, -100], [69, -201], [19, -102], [23, -392], [23, -392], [40, -648], [9, -135], [15, -254], [-207, 0], [-59, 0], [-165, 0], [-257, 0], [-333, 0], [-395, 0], [-396, 0], [-44, 0], [-470, 0], [-486, 0], [-486, -1], [-471, 0], [-440, 0], [-394, 0], [-334, 0], [-257, 0], [-165, 0], [-58, 0], [-165, 0], [-13, -13], [-25, -136], [-27, -150], [-60, -180], [-9, -50], [-33, -142], [-6, -47], [35, -169], [-17, 40], [-45, 71], [-6, 39], [5, 21], [13, 37], [2, 23], [0, 74], [6, 28], [26, 81], [58, 467], [13, 56], [4, 24], [2, 63]]], "transform": {"scale": [0.0007065209399973618, 0.0006380305553872046], "translate": [-17.104644334999932, 20.766913153000075]}, "objects": {"morocco_with_ws": {"type": "GeometryCollection", "geometries": [{"arcs": [[0]], "type": "Polygon"}]}}}