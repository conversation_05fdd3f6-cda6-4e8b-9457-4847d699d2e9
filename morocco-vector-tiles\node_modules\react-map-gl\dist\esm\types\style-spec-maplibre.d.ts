import type { BackgroundLayerSpecification as <PERSON><PERSON><PERSON><PERSON>, CircleLayerSpecification as <PERSON><PERSON>ay<PERSON>, FillLayerSpecification as Fill<PERSON>ayer, FillExtrusionLayerSpecification as FillExtrusionLayer, HeatmapLayerSpecification as <PERSON><PERSON>p<PERSON>ayer, HillshadeLayerSpecification as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LineLayerSpecification as <PERSON><PERSON>ay<PERSON>, RasterLayerSpecification as <PERSON><PERSON><PERSON>ay<PERSON>, SymbolLayerSpecification as SymbolLayer, GeoJSONSourceSpecification as GeoJSONSourceRaw, VideoSourceSpecification as VideoSourceRaw, ImageSourceSpecification as ImageSourceRaw, VectorSourceSpecification as VectorSourceRaw, RasterSourceSpecification as RasterSource, RasterDEMSourceSpecification as RasterDemSource } from '@maplibre/maplibre-gl-style-spec';
import { CanvasSourceSpecification as CanvasSourceRaw } from 'maplibre-gl';
export type { BackgroundLayer, CircleLayer, FillLayer, FillExtrusionLayer, <PERSON>map<PERSON>ay<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ay<PERSON>, <PERSON><PERSON><PERSON>ay<PERSON>, <PERSON><PERSON><PERSON><PERSON>ay<PERSON> };
export declare type AnyLayer = BackgroundLayer | CircleLayer | FillLayer | FillExtrusionLayer | HeatmapLayer | HillshadeLayer | LineLayer | RasterLayer | SymbolLayer;
export { GeoJSONSourceRaw, VideoSourceRaw, ImageSourceRaw, CanvasSourceRaw, VectorSourceRaw, RasterSource, RasterDemSource };
export declare type AnySource = GeoJSONSourceRaw | VideoSourceRaw | ImageSourceRaw | CanvasSourceRaw | VectorSourceRaw | RasterSource | RasterDemSource;
export type { StyleSpecification as MapStyle, LightSpecification as Light, TerrainSpecification as Terrain } from '@maplibre/maplibre-gl-style-spec';
export declare type Fog = never;
export declare type Projection = never;
