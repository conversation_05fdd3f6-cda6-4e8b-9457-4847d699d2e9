{"ast": null, "code": "import { useImperativeHandle, useRef, useEffect, forwardRef, memo } from 'react';\nimport { applyReactStyle } from '../utils/apply-react-style';\nimport useControl from './use-control';\nfunction GeolocateControl(props, ref) {\n  const thisRef = useRef({\n    props\n  });\n  const ctrl = useControl(({\n    mapLib\n  }) => {\n    const gc = new mapLib.GeolocateControl(props);\n    // Hack: fix GeolocateControl reuse\n    // When using React strict mode, the component is mounted twice.\n    // GeolocateControl's UI creation is asynchronous. Removing and adding it back causes the UI to be initialized twice.\n    // @ts-expect-error private method\n    const setupUI = gc._setupUI;\n    // @ts-expect-error private method\n    gc._setupUI = args => {\n      if (!gc._container.hasChildNodes()) {\n        setupUI(args);\n      }\n    };\n    gc.on('geolocate', e => {\n      var _a, _b;\n      (_b = (_a = thisRef.current.props).onGeolocate) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n    });\n    gc.on('error', e => {\n      var _a, _b;\n      (_b = (_a = thisRef.current.props).onError) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n    });\n    gc.on('outofmaxbounds', e => {\n      var _a, _b;\n      (_b = (_a = thisRef.current.props).onOutOfMaxBounds) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n    });\n    gc.on('trackuserlocationstart', e => {\n      var _a, _b;\n      (_b = (_a = thisRef.current.props).onTrackUserLocationStart) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n    });\n    gc.on('trackuserlocationend', e => {\n      var _a, _b;\n      (_b = (_a = thisRef.current.props).onTrackUserLocationEnd) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n    });\n    return gc;\n  }, {\n    position: props.position\n  });\n  thisRef.current.props = props;\n  useImperativeHandle(ref, () => ctrl, []);\n  useEffect(() => {\n    applyReactStyle(ctrl._container, props.style);\n  }, [props.style]);\n  return null;\n}\nexport default memo(forwardRef(GeolocateControl));", "map": {"version": 3, "names": ["useImperativeHandle", "useRef", "useEffect", "forwardRef", "memo", "applyReactStyle", "useControl", "GeolocateControl", "props", "ref", "thisRef", "ctrl", "mapLib", "gc", "setupUI", "_setupUI", "args", "_container", "hasChildNodes", "on", "e", "_b", "_a", "current", "onGeolocate", "call", "onError", "onOutOfMaxBounds", "onTrackUserLocationStart", "onTrackUserLocationEnd", "position", "style"], "sources": ["../../../src/components/geolocate-control.ts"], "sourcesContent": [null], "mappings": "AACA,SAAQA,mBAAmB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,IAAI,QAAO,OAAO;AAC9E,SAAQC,eAAe,QAAO,4BAA4B;AAC1D,OAAOC,UAAU,MAAM,eAAe;AAgCtC,SAASC,gBAAgBA,CACvBC,KAA+D,EAC/DC,GAAwB;EAExB,MAAMC,OAAO,GAAGT,MAAM,CAAC;IAACO;EAAK,CAAC,CAAC;EAE/B,MAAMG,IAAI,GAAGL,UAAU,CACrB,CAAC;IAACM;EAAM,CAAC,KAAI;IACX,MAAMC,EAAE,GAAG,IAAID,MAAM,CAACL,gBAAgB,CAACC,KAAK,CAAa;IAEzD;IACA;IACA;IACA;IACA,MAAMM,OAAO,GAAGD,EAAE,CAACE,QAAQ;IAC3B;IACAF,EAAE,CAACE,QAAQ,GAAGC,IAAI,IAAG;MACnB,IAAI,CAACH,EAAE,CAACI,UAAU,CAACC,aAAa,EAAE,EAAE;QAClCJ,OAAO,CAACE,IAAI,CAAC;;IAEjB,CAAC;IAEDH,EAAE,CAACM,EAAE,CAAC,WAAW,EAAEC,CAAC,IAAG;;MACrB,CAAAC,EAAA,IAAAC,EAAA,GAAAZ,OAAO,CAACa,OAAO,CAACf,KAAK,EAACgB,WAAW,cAAAH,EAAA,uBAAAA,EAAA,CAAAI,IAAA,CAAAH,EAAA,EAAGF,CAAmC,CAAC;IAC1E,CAAC,CAAC;IACFP,EAAE,CAACM,EAAE,CAAC,OAAO,EAAEC,CAAC,IAAG;;MACjB,CAAAC,EAAA,IAAAC,EAAA,GAAAZ,OAAO,CAACa,OAAO,CAACf,KAAK,EAACkB,OAAO,cAAAL,EAAA,uBAAAA,EAAA,CAAAI,IAAA,CAAAH,EAAA,EAAGF,CAAkC,CAAC;IACrE,CAAC,CAAC;IACFP,EAAE,CAACM,EAAE,CAAC,gBAAgB,EAAEC,CAAC,IAAG;;MAC1B,CAAAC,EAAA,IAAAC,EAAA,GAAAZ,OAAO,CAACa,OAAO,CAACf,KAAK,EAACmB,gBAAgB,cAAAN,EAAA,uBAAAA,EAAA,CAAAI,IAAA,CAAAH,EAAA,EAAGF,CAAmC,CAAC;IAC/E,CAAC,CAAC;IACFP,EAAE,CAACM,EAAE,CAAC,wBAAwB,EAAEC,CAAC,IAAG;;MAClC,CAAAC,EAAA,IAAAC,EAAA,GAAAZ,OAAO,CAACa,OAAO,CAACf,KAAK,EAACoB,wBAAwB,cAAAP,EAAA,uBAAAA,EAAA,CAAAI,IAAA,CAAAH,EAAA,EAAGF,CAA6B,CAAC;IACjF,CAAC,CAAC;IACFP,EAAE,CAACM,EAAE,CAAC,sBAAsB,EAAEC,CAAC,IAAG;;MAChC,CAAAC,EAAA,IAAAC,EAAA,GAAAZ,OAAO,CAACa,OAAO,CAACf,KAAK,EAACqB,sBAAsB,cAAAR,EAAA,uBAAAA,EAAA,CAAAI,IAAA,CAAAH,EAAA,EAAGF,CAA6B,CAAC;IAC/E,CAAC,CAAC;IAEF,OAAOP,EAAE;EACX,CAAC,EACD;IAACiB,QAAQ,EAAEtB,KAAK,CAACsB;EAAQ,CAAC,CAC3B;EAEDpB,OAAO,CAACa,OAAO,CAACf,KAAK,GAAGA,KAAK;EAE7BR,mBAAmB,CAACS,GAAG,EAAE,MAAME,IAAI,EAAE,EAAE,CAAC;EAExCT,SAAS,CAAC,MAAK;IACbG,eAAe,CAACM,IAAI,CAACM,UAAU,EAAET,KAAK,CAACuB,KAAK,CAAC;EAC/C,CAAC,EAAE,CAACvB,KAAK,CAACuB,KAAK,CAAC,CAAC;EAEjB,OAAO,IAAI;AACb;AAEA,eAAe3B,IAAI,CAACD,UAAU,CAACI,gBAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}