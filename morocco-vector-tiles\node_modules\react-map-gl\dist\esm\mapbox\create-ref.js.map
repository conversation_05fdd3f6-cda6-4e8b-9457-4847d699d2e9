{"version": 3, "file": "create-ref.js", "sourceRoot": "", "sources": ["../../../src/mapbox/create-ref.ts"], "names": [], "mappings": "AAUA,mEAAmE;AACnE,MAAM,WAAW,GAAG;IAClB,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,aAAa;IACb,sBAAsB;IACtB,eAAe;IACf,UAAU;IACV,WAAW;IACX,cAAc;IACd,UAAU;IACV,aAAa;IACb,mBAAmB;IACnB,WAAW;IACX,kBAAkB;IAClB,mBAAmB;IACnB,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,QAAQ;CACA,CAAC;AAMX,MAAM,CAAC,OAAO,UAAU,SAAS,CAI/B,WAA6C;IAC7C,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,IAAI,CAAC;KACb;IAED,MAAM,GAAG,GAAG,WAAW,CAAC,GAAgC,CAAC;IACzD,MAAM,MAAM,GAAQ;QAClB,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG;QAEjB,gDAAgD;QAChD,SAAS,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM;QAC7C,OAAO,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI;QACzC,UAAU,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO;QAC/C,QAAQ,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK;QAC3C,UAAU,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO;QAC/C,SAAS,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,SAAS,EAAE;QAClD,OAAO,EAAE,CAAC,MAAkB,EAAE,EAAE;YAC9B,MAAM,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC;YACzB,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;YACtC,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACnC,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,SAAS,EAAE,CAAC,KAAgB,EAAE,EAAE;YAC9B,MAAM,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC;YACzB,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;YACtC,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACpC,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,8CAA8C;QAC9C,qBAAqB,EAAE,CAAC,MAAkB,EAAE,OAAa,EAAE,EAAE;YAC3D,MAAM,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC;YACzB,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;YACtC,MAAM,MAAM,GAAG,GAAG,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC1D,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,qBAAqB,EAAE,CAAC,QAAc,EAAE,OAAa,EAAE,EAAE;YACvD,MAAM,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC;YACzB,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;YACtC,MAAM,MAAM,GAAG,GAAG,CAAC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC5D,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;KACF,CAAC;IAEF,KAAK,MAAM,GAAG,IAAI,cAAc,CAAC,GAAG,CAAC,EAAE;QACrC,mBAAmB;QACnB,IAAI,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAClD,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClC;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,cAAc,CAAC,GAAW;IACjC,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;IAEjC,IAAI,KAAK,GAAG,GAAG,CAAC;IAChB,OAAO,KAAK,EAAE;QACZ,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;YACnD,IACE,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG;gBACd,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,UAAU;gBAC9B,GAAG,KAAK,MAAM;gBACd,GAAG,KAAK,kBAAkB,EAC1B;gBACA,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACjB;SACF;QACD,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;KACtC;IACD,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5B,CAAC"}