{"version": 3, "file": "layer.js", "sourceRoot": "", "sources": ["../../../src/components/layer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAC,MAAM,OAAO,CAAC;AACvE,OAAO,EAAC,UAAU,EAAC,MAAM,OAAO,CAAC;AACjC,OAAO,MAAM,MAAM,iBAAiB,CAAC;AACrC,OAAO,EAAC,SAAS,EAAC,MAAM,qBAAqB,CAAC;AAc9C,+CAA+C;AAC/C,SAAS,WAAW,CAClB,GAAgB,EAChB,EAAU,EACV,KAAyB,EACzB,SAA6B;IAE7B,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;IACtD,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;IAE5D,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC1D,OAAO;KACR;IAED,MAAM,EAAC,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAC,GAAG,KAAK,CAAC;IAE5E,IAAI,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE;QACnC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;KAC7B;IACD,IAAI,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE;QAC/B,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,IAAI,EAAE,CAAC;QAC1C,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC5C,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aAC7C;SACF;QACD,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBAC/B,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;aAC3C;SACF;KACF;IACD,IAAI,KAAK,KAAK,SAAS,CAAC,KAAK,EAAE;QAC7B,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC;QACxC,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC1C,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3C;SACF;QACD,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBAC9B,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;aAC1C;SACF;KACF;IAED,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE;QACxC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;KAC3B;IACD,IAAI,OAAO,KAAK,SAAS,CAAC,OAAO,IAAI,OAAO,KAAK,SAAS,CAAC,OAAO,EAAE;QAClE,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;KAC7C;AACH,CAAC;AAED,SAAS,WAAW,CAClB,GAAgB,EAChB,EAAU,EACV,KAAyB;IAEzB,aAAa;IACb,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE;QAC3F,MAAM,OAAO,GAAuB,EAAC,GAAG,KAAK,EAAE,EAAE,EAAC,CAAC;QACnD,OAAO,OAAO,CAAC,QAAQ,CAAC;QAExB,aAAa;QACb,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;KACvC;AACH,CAAC;AAED,8CAA8C;AAE9C,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB,SAAS,KAAK,CAAwB,KAAgD;IACpF,MAAM,GAAG,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;IAChD,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEvC,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,aAAa,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAExE,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,GAAG,EAAE;YACP,MAAM,WAAW,GAAG,GAAG,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YACjE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACjC,WAAW,EAAE,CAAC;YAEd,OAAO,GAAG,EAAE;gBACV,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;gBAClC,aAAa;gBACb,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;oBACtD,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;iBACrB;YACH,CAAC,CAAC;SACH;QACD,OAAO,SAAS,CAAC;IACnB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAEV,aAAa;IACb,MAAM,KAAK,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACnD,IAAI,KAAK,EAAE;QACT,IAAI;YACF,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;SAC/C;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,sBAAsB;SAC5C;KACF;SAAM;QACL,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;KAC7B;IAED,4BAA4B;IAC5B,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;IAEzB,OAAO,IAAI,CAAC;AACd,CAAC;AAED,eAAe,KAAK,CAAC"}