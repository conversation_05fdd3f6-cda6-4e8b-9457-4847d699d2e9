{"ast": null, "code": "import { deepEqual } from './deep-equal';\n/**\n * Make a copy of a transform\n * @param tr\n */\nexport function cloneTransform(tr) {\n  const newTransform = tr.clone();\n  // Work around mapbox bug - this value is not assigned in clone(), only in resize()\n  newTransform.pixelsToGLUnits = tr.pixelsToGLUnits;\n  return newTransform;\n}\n/**\n * Copy projection from one transform to another. This only applies to mapbox-gl transforms\n * @param src the transform to copy projection settings from\n * @param dest to transform to copy projection settings to\n */\nexport function syncProjection(src, dest) {\n  if (!src.getProjection) {\n    return;\n  }\n  const srcProjection = src.getProjection();\n  const destProjection = dest.getProjection();\n  if (!deepEqual(srcProjection, destProjection)) {\n    dest.setProjection(srcProjection);\n  }\n}\n/**\n * Capture a transform's current state\n * @param transform\n * @returns descriptor of the view state\n */\nexport function transformToViewState(tr) {\n  return {\n    longitude: tr.center.lng,\n    latitude: tr.center.lat,\n    zoom: tr.zoom,\n    pitch: tr.pitch,\n    bearing: tr.bearing,\n    padding: tr.padding\n  };\n}\n/* eslint-disable complexity */\n/**\n * Mutate a transform to match the given view state\n * @param transform\n * @param viewState\n * @returns true if the transform has changed\n */\nexport function applyViewStateToTransform(tr, props) {\n  const v = props.viewState || props;\n  let changed = false;\n  if ('longitude' in v && 'latitude' in v) {\n    const center = tr.center;\n    // @ts-ignore\n    tr.center = new center.constructor(v.longitude, v.latitude);\n    changed = changed || center !== tr.center;\n  }\n  if ('zoom' in v) {\n    const zoom = tr.zoom;\n    tr.zoom = v.zoom;\n    changed = changed || zoom !== tr.zoom;\n  }\n  if ('bearing' in v) {\n    const bearing = tr.bearing;\n    tr.bearing = v.bearing;\n    changed = changed || bearing !== tr.bearing;\n  }\n  if ('pitch' in v) {\n    const pitch = tr.pitch;\n    tr.pitch = v.pitch;\n    changed = changed || pitch !== tr.pitch;\n  }\n  if (v.padding && !tr.isPaddingEqual(v.padding)) {\n    changed = true;\n    tr.padding = v.padding;\n  }\n  return changed;\n}", "map": {"version": 3, "names": ["deepEqual", "cloneTransform", "tr", "newTransform", "clone", "pixelsToGLUnits", "syncProjection", "src", "dest", "getProjection", "srcProjection", "destProjection", "setProjection", "transformToViewState", "longitude", "center", "lng", "latitude", "lat", "zoom", "pitch", "bearing", "padding", "applyViewStateToTransform", "props", "v", "viewState", "changed", "constructor", "isPaddingEqual"], "sources": ["C:\\Users\\<USER>\\Downloads\\morocco-complete-map-geojson-topojson-main\\morocco-vector-tiles\\node_modules\\react-map-gl\\src\\utils\\transform.ts"], "sourcesContent": ["import type {MapboxProps} from '../mapbox/mapbox';\nimport type {Transform, ViewState} from '../types';\nimport {deepEqual} from './deep-equal';\n\n/**\n * Make a copy of a transform\n * @param tr\n */\nexport function cloneTransform(tr: Transform): Transform {\n  const newTransform = tr.clone();\n  // Work around mapbox bug - this value is not assigned in clone(), only in resize()\n  newTransform.pixelsToGLUnits = tr.pixelsToGLUnits;\n  return newTransform;\n}\n\n/**\n * Copy projection from one transform to another. This only applies to mapbox-gl transforms\n * @param src the transform to copy projection settings from\n * @param dest to transform to copy projection settings to\n */\nexport function syncProjection(src: Transform, dest: Transform): void {\n  if (!src.getProjection) {\n    return;\n  }\n  const srcProjection = src.getProjection();\n  const destProjection = dest.getProjection();\n\n  if (!deepEqual(srcProjection, destProjection)) {\n    dest.setProjection(srcProjection);\n  }\n}\n\n/**\n * Capture a transform's current state\n * @param transform\n * @returns descriptor of the view state\n */\nexport function transformToViewState(tr: Transform): ViewState {\n  return {\n    longitude: tr.center.lng,\n    latitude: tr.center.lat,\n    zoom: tr.zoom,\n    pitch: tr.pitch,\n    bearing: tr.bearing,\n    padding: tr.padding\n  };\n}\n\n/* eslint-disable complexity */\n/**\n * Mutate a transform to match the given view state\n * @param transform\n * @param viewState\n * @returns true if the transform has changed\n */\nexport function applyViewStateToTransform(tr: Transform, props: MapboxProps): boolean {\n  const v: Partial<ViewState> = props.viewState || props;\n  let changed = false;\n\n  if ('longitude' in v && 'latitude' in v) {\n    const center = tr.center;\n    // @ts-ignore\n    tr.center = new center.constructor(v.longitude, v.latitude);\n    changed = changed || center !== tr.center;\n  }\n  if ('zoom' in v) {\n    const zoom = tr.zoom;\n    tr.zoom = v.zoom;\n    changed = changed || zoom !== tr.zoom;\n  }\n  if ('bearing' in v) {\n    const bearing = tr.bearing;\n    tr.bearing = v.bearing;\n    changed = changed || bearing !== tr.bearing;\n  }\n  if ('pitch' in v) {\n    const pitch = tr.pitch;\n    tr.pitch = v.pitch;\n    changed = changed || pitch !== tr.pitch;\n  }\n  if (v.padding && !tr.isPaddingEqual(v.padding)) {\n    changed = true;\n    tr.padding = v.padding;\n  }\n  return changed;\n}\n"], "mappings": "AAEA,SAAQA,SAAS,QAAO,cAAc;AAEtC;;;;AAIA,OAAM,SAAUC,cAAcA,CAACC,EAAa;EAC1C,MAAMC,YAAY,GAAGD,EAAE,CAACE,KAAK,EAAE;EAC/B;EACAD,YAAY,CAACE,eAAe,GAAGH,EAAE,CAACG,eAAe;EACjD,OAAOF,YAAY;AACrB;AAEA;;;;;AAKA,OAAM,SAAUG,cAAcA,CAACC,GAAc,EAAEC,IAAe;EAC5D,IAAI,CAACD,GAAG,CAACE,aAAa,EAAE;IACtB;;EAEF,MAAMC,aAAa,GAAGH,GAAG,CAACE,aAAa,EAAE;EACzC,MAAME,cAAc,GAAGH,IAAI,CAACC,aAAa,EAAE;EAE3C,IAAI,CAACT,SAAS,CAACU,aAAa,EAAEC,cAAc,CAAC,EAAE;IAC7CH,IAAI,CAACI,aAAa,CAACF,aAAa,CAAC;;AAErC;AAEA;;;;;AAKA,OAAM,SAAUG,oBAAoBA,CAACX,EAAa;EAChD,OAAO;IACLY,SAAS,EAAEZ,EAAE,CAACa,MAAM,CAACC,GAAG;IACxBC,QAAQ,EAAEf,EAAE,CAACa,MAAM,CAACG,GAAG;IACvBC,IAAI,EAAEjB,EAAE,CAACiB,IAAI;IACbC,KAAK,EAAElB,EAAE,CAACkB,KAAK;IACfC,OAAO,EAAEnB,EAAE,CAACmB,OAAO;IACnBC,OAAO,EAAEpB,EAAE,CAACoB;GACb;AACH;AAEA;AACA;;;;;;AAMA,OAAM,SAAUC,yBAAyBA,CAACrB,EAAa,EAAEsB,KAAkB;EACzE,MAAMC,CAAC,GAAuBD,KAAK,CAACE,SAAS,IAAIF,KAAK;EACtD,IAAIG,OAAO,GAAG,KAAK;EAEnB,IAAI,WAAW,IAAIF,CAAC,IAAI,UAAU,IAAIA,CAAC,EAAE;IACvC,MAAMV,MAAM,GAAGb,EAAE,CAACa,MAAM;IACxB;IACAb,EAAE,CAACa,MAAM,GAAG,IAAIA,MAAM,CAACa,WAAW,CAACH,CAAC,CAACX,SAAS,EAAEW,CAAC,CAACR,QAAQ,CAAC;IAC3DU,OAAO,GAAGA,OAAO,IAAIZ,MAAM,KAAKb,EAAE,CAACa,MAAM;;EAE3C,IAAI,MAAM,IAAIU,CAAC,EAAE;IACf,MAAMN,IAAI,GAAGjB,EAAE,CAACiB,IAAI;IACpBjB,EAAE,CAACiB,IAAI,GAAGM,CAAC,CAACN,IAAI;IAChBQ,OAAO,GAAGA,OAAO,IAAIR,IAAI,KAAKjB,EAAE,CAACiB,IAAI;;EAEvC,IAAI,SAAS,IAAIM,CAAC,EAAE;IAClB,MAAMJ,OAAO,GAAGnB,EAAE,CAACmB,OAAO;IAC1BnB,EAAE,CAACmB,OAAO,GAAGI,CAAC,CAACJ,OAAO;IACtBM,OAAO,GAAGA,OAAO,IAAIN,OAAO,KAAKnB,EAAE,CAACmB,OAAO;;EAE7C,IAAI,OAAO,IAAII,CAAC,EAAE;IAChB,MAAML,KAAK,GAAGlB,EAAE,CAACkB,KAAK;IACtBlB,EAAE,CAACkB,KAAK,GAAGK,CAAC,CAACL,KAAK;IAClBO,OAAO,GAAGA,OAAO,IAAIP,KAAK,KAAKlB,EAAE,CAACkB,KAAK;;EAEzC,IAAIK,CAAC,CAACH,OAAO,IAAI,CAACpB,EAAE,CAAC2B,cAAc,CAACJ,CAAC,CAACH,OAAO,CAAC,EAAE;IAC9CK,OAAO,GAAG,IAAI;IACdzB,EAAE,CAACoB,OAAO,GAAGG,CAAC,CAACH,OAAO;;EAExB,OAAOK,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}