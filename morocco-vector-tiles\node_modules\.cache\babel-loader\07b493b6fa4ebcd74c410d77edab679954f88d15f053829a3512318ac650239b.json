{"ast": null, "code": "/**\n * Compare two points\n * @param a\n * @param b\n * @returns true if the points are equal\n */\nexport function arePointsEqual(a, b) {\n  const ax = Array.isArray(a) ? a[0] : a ? a.x : 0;\n  const ay = Array.isArray(a) ? a[1] : a ? a.y : 0;\n  const bx = Array.isArray(b) ? b[0] : b ? b.x : 0;\n  const by = Array.isArray(b) ? b[1] : b ? b.y : 0;\n  return ax === bx && ay === by;\n}\n/* eslint-disable complexity */\n/**\n * Compare any two objects\n * @param a\n * @param b\n * @returns true if the objects are deep equal\n */\nexport function deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (!a || !b) {\n    return false;\n  }\n  if (Array.isArray(a)) {\n    if (!Array.isArray(b) || a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (!deepEqual(a[i], b[i])) {\n        return false;\n      }\n    }\n    return true;\n  } else if (Array.isArray(b)) {\n    return false;\n  }\n  if (typeof a === 'object' && typeof b === 'object') {\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    if (aKeys.length !== bKeys.length) {\n      return false;\n    }\n    for (const key of aKeys) {\n      if (!b.hasOwnProperty(key)) {\n        return false;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["arePointsEqual", "a", "b", "ax", "Array", "isArray", "x", "ay", "y", "bx", "by", "deepEqual", "length", "i", "a<PERSON><PERSON><PERSON>", "Object", "keys", "b<PERSON><PERSON><PERSON>", "key", "hasOwnProperty"], "sources": ["C:\\Users\\<USER>\\Downloads\\morocco-complete-map-geojson-topojson-main\\morocco-vector-tiles\\node_modules\\react-map-gl\\src\\utils\\deep-equal.ts"], "sourcesContent": ["import type {PointLike} from '../types';\n\n/**\n * Compare two points\n * @param a\n * @param b\n * @returns true if the points are equal\n */\nexport function arePointsEqual(a?: PointLike, b?: PointLike): boolean {\n  const ax = Array.isArray(a) ? a[0] : a ? a.x : 0;\n  const ay = Array.isArray(a) ? a[1] : a ? a.y : 0;\n  const bx = Array.isArray(b) ? b[0] : b ? b.x : 0;\n  const by = Array.isArray(b) ? b[1] : b ? b.y : 0;\n  return ax === bx && ay === by;\n}\n\n/* eslint-disable complexity */\n/**\n * Compare any two objects\n * @param a\n * @param b\n * @returns true if the objects are deep equal\n */\nexport function deepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true;\n  }\n  if (!a || !b) {\n    return false;\n  }\n  if (Array.isArray(a)) {\n    if (!Array.isArray(b) || a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (!deepEqual(a[i], b[i])) {\n        return false;\n      }\n    }\n    return true;\n  } else if (Array.isArray(b)) {\n    return false;\n  }\n  if (typeof a === 'object' && typeof b === 'object') {\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    if (aKeys.length !== bKeys.length) {\n      return false;\n    }\n    for (const key of aKeys) {\n      if (!b.hasOwnProperty(key)) {\n        return false;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return false;\n}\n"], "mappings": "AAEA;;;;;;AAMA,OAAM,SAAUA,cAAcA,CAACC,CAAa,EAAEC,CAAa;EACzD,MAAMC,EAAE,GAAGC,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACK,CAAC,GAAG,CAAC;EAChD,MAAMC,EAAE,GAAGH,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACO,CAAC,GAAG,CAAC;EAChD,MAAMC,EAAE,GAAGL,KAAK,CAACC,OAAO,CAACH,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACI,CAAC,GAAG,CAAC;EAChD,MAAMI,EAAE,GAAGN,KAAK,CAACC,OAAO,CAACH,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACM,CAAC,GAAG,CAAC;EAChD,OAAOL,EAAE,KAAKM,EAAE,IAAIF,EAAE,KAAKG,EAAE;AAC/B;AAEA;AACA;;;;;;AAMA,OAAM,SAAUC,SAASA,CAACV,CAAM,EAAEC,CAAM;EACtC,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;;EAEb,IAAI,CAACD,CAAC,IAAI,CAACC,CAAC,EAAE;IACZ,OAAO,KAAK;;EAEd,IAAIE,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,EAAE;IACpB,IAAI,CAACG,KAAK,CAACC,OAAO,CAACH,CAAC,CAAC,IAAID,CAAC,CAACW,MAAM,KAAKV,CAAC,CAACU,MAAM,EAAE;MAC9C,OAAO,KAAK;;IAEd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,CAAC,CAACW,MAAM,EAAEC,CAAC,EAAE,EAAE;MACjC,IAAI,CAACF,SAAS,CAACV,CAAC,CAACY,CAAC,CAAC,EAAEX,CAAC,CAACW,CAAC,CAAC,CAAC,EAAE;QAC1B,OAAO,KAAK;;;IAGhB,OAAO,IAAI;GACZ,MAAM,IAAIT,KAAK,CAACC,OAAO,CAACH,CAAC,CAAC,EAAE;IAC3B,OAAO,KAAK;;EAEd,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IAClD,MAAMY,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACf,CAAC,CAAC;IAC5B,MAAMgB,KAAK,GAAGF,MAAM,CAACC,IAAI,CAACd,CAAC,CAAC;IAC5B,IAAIY,KAAK,CAACF,MAAM,KAAKK,KAAK,CAACL,MAAM,EAAE;MACjC,OAAO,KAAK;;IAEd,KAAK,MAAMM,GAAG,IAAIJ,KAAK,EAAE;MACvB,IAAI,CAACZ,CAAC,CAACiB,cAAc,CAACD,GAAG,CAAC,EAAE;QAC1B,OAAO,KAAK;;MAEd,IAAI,CAACP,SAAS,CAACV,CAAC,CAACiB,GAAG,CAAC,EAAEhB,CAAC,CAACgB,GAAG,CAAC,CAAC,EAAE;QAC9B,OAAO,KAAK;;;IAGhB,OAAO,IAAI;;EAEb,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}