import React, { useState, useEffect, useRef } from 'react';
import Map, { Source, Layer } from 'react-map-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { combineMapData, createVectorTiles, generateTileMetadata } from '../utils/dataProcessor';

const MoroccoMap = () => {
  const [mapData, setMapData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [viewState, setViewState] = useState({
    longitude: -7.0926,
    latitude: 31.7917,
    zoom: 5,
    pitch: 0,
    bearing: 0
  });

  const mapRef = useRef();

  // Charger les données géographiques
  useEffect(() => {
    const loadMapData = async () => {
      try {
        setLoading(true);
        
        // Charger les données du Maroc et du monde
        const [moroccoResponse, worldResponse] = await Promise.all([
          fetch('/morocco_map.geojson'),
          fetch('/world_map_detailed.geojson')
        ]);

        if (!moroccoResponse.ok || !worldResponse.ok) {
          throw new Error('Erreur lors du chargement des données');
        }

        const moroccoData = await moroccoResponse.json();
        const worldData = await worldResponse.json();

        // Combiner et traiter les données
        const combinedData = combineMapData(moroccoData, worldData);
        const processedData = createVectorTiles(combinedData, 6);
        
        setMapData(processedData);
        
        // Générer les métadonnées
        const metadata = generateTileMetadata(processedData);
        console.log('Métadonnées des tuiles:', metadata);
        
      } catch (err) {
        console.error('Erreur lors du chargement des données:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadMapData();
  }, []);

  // Style des couches de la carte
  const countryLayerStyle = {
    id: 'countries-fill',
    type: 'fill',
    paint: {
      'fill-color': [
        'case',
        ['==', ['get', 'name'], 'Morocco'],
        '#2563eb', // Bleu pour le Maroc
        ['==', ['get', 'detailed'], true],
        '#3b82f6', // Bleu plus clair pour les détails du Maroc
        '#f3f4f6'  // Gris clair pour les autres pays
      ],
      'fill-opacity': [
        'case',
        ['==', ['get', 'name'], 'Morocco'],
        0.8,
        0.6
      ]
    }
  };

  const countryBorderStyle = {
    id: 'countries-border',
    type: 'line',
    paint: {
      'line-color': [
        'case',
        ['==', ['get', 'name'], 'Morocco'],
        '#1d4ed8',
        '#6b7280'
      ],
      'line-width': [
        'case',
        ['==', ['get', 'name'], 'Morocco'],
        2,
        1
      ]
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh', 
        backgroundColor: '#f3f4f6' 
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #2563eb',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280' }}>Chargement des tuiles vectorielles...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh', 
        backgroundColor: '#f3f4f6' 
      }}>
        <div style={{ textAlign: 'center', color: '#dc2626' }}>
          <p style={{ fontSize: '20px', fontWeight: '600', marginBottom: '8px' }}>Erreur</p>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height: '100vh', width: '100%' }}>
      <Map
        ref={mapRef}
        {...viewState}
        onMove={evt => setViewState(evt.viewState)}
        style={{ width: '100%', height: '100%' }}
        mapStyle="mapbox://styles/mapbox/light-v11"
        mapboxAccessToken="pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw"
      >
        {mapData && (
          <Source
            id="morocco-region"
            type="geojson"
            data={mapData}
          >
            <Layer {...countryLayerStyle} />
            <Layer {...countryBorderStyle} />
          </Source>
        )}
      </Map>
      
      {/* Panneau d'informations */}
      <div style={{
        position: 'absolute',
        top: '16px',
        left: '16px',
        backgroundColor: 'white',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        maxWidth: '320px'
      }}>
        <h2 style={{ 
          fontSize: '18px', 
          fontWeight: 'bold', 
          color: '#1f2937', 
          marginBottom: '8px' 
        }}>
          Carte Vectorielle du Maroc
        </h2>
        <p style={{ 
          fontSize: '14px', 
          color: '#6b7280', 
          marginBottom: '8px' 
        }}>
          Région incluant le Maroc, l'Europe du Sud, l'Algérie, la Mauritanie et l'océan Atlantique
        </p>
        {mapData && (
          <p style={{ fontSize: '12px', color: '#9ca3af' }}>
            {mapData.features.length} entités géographiques chargées
          </p>
        )}
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default MoroccoMap;
