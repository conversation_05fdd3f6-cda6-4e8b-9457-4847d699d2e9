import React, { useState, useEffect } from 'react';

const MoroccoMap = () => {
  const [tileServerRunning, setTileServerRunning] = useState(false);
  const [loading, setLoading] = useState(true);

  // Vérifier si le serveur de tuiles est en cours d'exécution
  useEffect(() => {
    const checkTileServer = async () => {
      try {
        const response = await fetch('http://localhost:3001/tiles/metadata');
        if (response.ok) {
          setTileServerRunning(true);
          console.log('✅ Serveur de tuiles détecté');
        }
      } catch (error) {
        console.log('⚠️ Serveur de tuiles non disponible');
        setTileServerRunning(false);
      } finally {
        setLoading(false);
      }
    };

    checkTileServer();
  }, []);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        backgroundColor: '#f3f4f6'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #2563eb',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280' }}>Chargement des tuiles vectorielles...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height: '100vh', width: '100%', backgroundColor: '#f0f8ff' }}>
      {/* Panneau d'informations */}
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        backgroundColor: 'white',
        padding: '32px',
        borderRadius: '12px',
        boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
        textAlign: 'center',
        maxWidth: '500px'
      }}>
        <h1 style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          marginBottom: '16px'
        }}>
          🗺️ Système de Tuiles Vectorielles du Maroc
        </h1>

        {/* Statut du serveur de tuiles */}
        <div style={{
          fontSize: '16px',
          marginBottom: '16px',
          padding: '12px 16px',
          borderRadius: '8px',
          backgroundColor: tileServerRunning ? '#dcfce7' : '#fef3c7',
          color: tileServerRunning ? '#166534' : '#92400e'
        }}>
          {tileServerRunning ? '🟢 Serveur de tuiles actif' : '🟡 Serveur de tuiles non disponible'}
        </div>

        <div style={{ fontSize: '14px', color: '#6b7280', lineHeight: '1.6' }}>
          <p><strong>Serveur de tuiles :</strong> http://localhost:3001</p>
          <p><strong>Application React :</strong> http://localhost:3000</p>
          <br />
          {tileServerRunning ? (
            <div>
              <p>✅ Tuiles raster disponibles</p>
              <p>✅ Tuiles vectorielles disponibles</p>
              <p>🌐 Fonctionne sans internet</p>
              <br />
              <p><strong>URLs des tuiles :</strong></p>
              <p>• Raster: /tiles/raster/{'{z}/{x}/{y}.png'}</p>
              <p>• Vector: /tiles/vector/{'{z}/{x}/{y}.json'}</p>
              <p>• Métadonnées: /tiles/metadata</p>
            </div>
          ) : (
            <div>
              <p>⚠️ Démarrez le serveur de tuiles avec :</p>
              <code style={{ backgroundColor: '#f3f4f6', padding: '4px 8px', borderRadius: '4px' }}>
                npm run tile-server
              </code>
            </div>
          )}
        </div>
      </div>

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default MoroccoMap;
      ],
      'line-width': [
        'case',
        ['==', ['get', 'name'], 'Morocco'],
        2,
        1
      ]
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh', 
        backgroundColor: '#f3f4f6' 
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #2563eb',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280' }}>Chargement des tuiles vectorielles...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh', 
        backgroundColor: '#f3f4f6' 
      }}>
        <div style={{ textAlign: 'center', color: '#dc2626' }}>
          <p style={{ fontSize: '20px', fontWeight: '600', marginBottom: '8px' }}>Erreur</p>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  // Style de carte personnalisé pour les tuiles raster locales
  const customMapStyle = tileServerRunning ? {
    version: 8,
    sources: {
      'raster-tiles': {
        type: 'raster',
        tiles: ['http://localhost:3001/tiles/raster/{z}/{x}/{y}.png'],
        tileSize: 256,
        minzoom: 0,
        maxzoom: 14
      },
      'vector-tiles': {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: []
        }
      }
    },
    layers: [
      {
        id: 'background',
        type: 'background',
        paint: {
          'background-color': '#f0f8ff'
        }
      },
      {
        id: 'raster-layer',
        type: 'raster',
        source: 'raster-tiles',
        paint: {
          'raster-opacity': 0.8
        }
      }
    ]
  } : "mapbox://styles/mapbox/light-v11";

  return (
    <div style={{ height: '100vh', width: '100%' }}>
      <Map
        ref={mapRef}
        {...viewState}
        onMove={evt => setViewState(evt.viewState)}
        style={{ width: '100%', height: '100%' }}
        mapStyle={customMapStyle}
        mapboxAccessToken="pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw"
      >
        {/* Tuiles vectorielles du serveur local */}
        {tileServerRunning && (
          <Source
            id="vector-tiles-server"
            type="vector"
            tiles={['http://localhost:3001/tiles/vector/{z}/{x}/{y}.json']}
            minzoom={0}
            maxzoom={14}
          >
            <Layer {...countryLayerStyle} />
            <Layer {...countryBorderStyle} />
          </Source>
        )}

        {/* Données locales comme fallback */}
        {!tileServerRunning && mapData && (
          <Source
            id="morocco-region"
            type="geojson"
            data={mapData}
          >
            <Layer {...countryLayerStyle} />
            <Layer {...countryBorderStyle} />
          </Source>
        )}
      </Map>
      
      {/* Panneau d'informations */}
      <div style={{
        position: 'absolute',
        top: '16px',
        left: '16px',
        backgroundColor: 'white',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        maxWidth: '320px'
      }}>
        <h2 style={{
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1f2937',
          marginBottom: '8px'
        }}>
          Carte Vectorielle du Maroc
        </h2>
        <p style={{
          fontSize: '14px',
          color: '#6b7280',
          marginBottom: '8px'
        }}>
          Région incluant le Maroc, l'Europe du Sud, l'Algérie, la Mauritanie et l'océan Atlantique
        </p>

        {/* Statut du serveur de tuiles */}
        <div style={{
          fontSize: '12px',
          marginBottom: '8px',
          padding: '4px 8px',
          borderRadius: '4px',
          backgroundColor: tileServerRunning ? '#dcfce7' : '#fef3c7',
          color: tileServerRunning ? '#166534' : '#92400e'
        }}>
          {tileServerRunning ? '🟢 Serveur de tuiles actif' : '🟡 Mode données locales'}
        </div>

        {mapData && !tileServerRunning && (
          <p style={{ fontSize: '12px', color: '#9ca3af' }}>
            {mapData.features.length} entités géographiques chargées
          </p>
        )}

        {tileServerRunning && (
          <div style={{ fontSize: '12px', color: '#9ca3af' }}>
            <p>✅ Tuiles raster disponibles</p>
            <p>✅ Tuiles vectorielles disponibles</p>
            <p>🌐 Fonctionne sans internet</p>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default MoroccoMap;
