{"ast": null, "code": "import { useEffect, memo } from 'react';\nimport { applyReactStyle } from '../utils/apply-react-style';\nimport useControl from './use-control';\nfunction AttributionControl(props) {\n  const ctrl = useControl(({\n    mapLib\n  }) => new mapLib.AttributionControl(props), {\n    position: props.position\n  });\n  useEffect(() => {\n    applyReactStyle(ctrl._container, props.style);\n  }, [props.style]);\n  return null;\n}\nexport default memo(AttributionControl);", "map": {"version": 3, "names": ["useEffect", "memo", "applyReactStyle", "useControl", "AttributionControl", "props", "ctrl", "mapLib", "position", "_container", "style"], "sources": ["../../../src/components/attribution-control.ts"], "sourcesContent": [null], "mappings": "AACA,SAAQA,SAAS,EAAEC,IAAI,QAAO,OAAO;AACrC,SAAQC,eAAe,QAAO,4BAA4B;AAC1D,OAAOC,UAAU,MAAM,eAAe;AAWtC,SAASC,kBAAkBA,CACzBC,KAAyD;EAEzD,MAAMC,IAAI,GAAGH,UAAU,CACrB,CAAC;IAACI;EAAM,CAAC,KAAK,IAAIA,MAAM,CAACH,kBAAkB,CAACC,KAAK,CAAa,EAC9D;IACEG,QAAQ,EAAEH,KAAK,CAACG;GACjB,CACF;EAEDR,SAAS,CAAC,MAAK;IACbE,eAAe,CAACI,IAAI,CAACG,UAAU,EAAEJ,KAAK,CAACK,KAAK,CAAC;EAC/C,CAAC,EAAE,CAACL,KAAK,CAACK,KAAK,CAAC,CAAC;EAEjB,OAAO,IAAI;AACb;AAEA,eAAeT,IAAI,CAACG,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}