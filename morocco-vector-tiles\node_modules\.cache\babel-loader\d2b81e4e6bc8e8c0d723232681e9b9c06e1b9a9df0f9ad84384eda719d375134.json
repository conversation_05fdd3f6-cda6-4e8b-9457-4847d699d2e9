{"ast": null, "code": "import { useContext, useMemo, useEffect } from 'react';\nimport { MapContext } from './map';\nfunction useControl(onCreate, arg1, arg2, arg3) {\n  const context = useContext(MapContext);\n  const ctrl = useMemo(() => onCreate(context), []);\n  useEffect(() => {\n    const opts = arg3 || arg2 || arg1;\n    const onAdd = typeof arg1 === 'function' && typeof arg2 === 'function' ? arg1 : null;\n    const onRemove = typeof arg2 === 'function' ? arg2 : typeof arg1 === 'function' ? arg1 : null;\n    const {\n      map\n    } = context;\n    if (!map.hasControl(ctrl)) {\n      map.addControl(ctrl, opts === null || opts === void 0 ? void 0 : opts.position);\n      if (onAdd) {\n        onAdd(context);\n      }\n    }\n    return () => {\n      if (onRemove) {\n        onRemove(context);\n      }\n      // Map might have been removed (parent effects are destroyed before child ones)\n      if (map.hasControl(ctrl)) {\n        map.removeControl(ctrl);\n      }\n    };\n  }, []);\n  return ctrl;\n}\nexport default useControl;", "map": {"version": 3, "names": ["useContext", "useMemo", "useEffect", "MapContext", "useControl", "onCreate", "arg1", "arg2", "arg3", "context", "ctrl", "opts", "onAdd", "onRemove", "map", "hasControl", "addControl", "position", "removeControl"], "sources": ["../../../src/components/use-control.ts"], "sourcesContent": [null], "mappings": "AAAA,SAAQA,UAAU,EAAEC,OAAO,EAAEC,SAAS,QAAO,OAAO;AAEpD,SAAQC,UAAU,QAAO,OAAO;AAyBhC,SAASC,UAAUA,CACjBC,QAAyC,EACzCC,IAA4D,EAC5DC,IAA4D,EAC5DC,IAAqB;EAErB,MAAMC,OAAO,GAAGT,UAAU,CAACG,UAAU,CAAC;EACtC,MAAMO,IAAI,GAAGT,OAAO,CAAC,MAAMI,QAAQ,CAACI,OAAO,CAAC,EAAE,EAAE,CAAC;EAEjDP,SAAS,CAAC,MAAK;IACb,MAAMS,IAAI,GAAIH,IAAI,IAAID,IAAI,IAAID,IAAuB;IACrD,MAAMM,KAAK,GAAG,OAAON,IAAI,KAAK,UAAU,IAAI,OAAOC,IAAI,KAAK,UAAU,GAAGD,IAAI,GAAG,IAAI;IACpF,MAAMO,QAAQ,GAAG,OAAON,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAG,OAAOD,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAG,IAAI;IAE7F,MAAM;MAACQ;IAAG,CAAC,GAAGL,OAAO;IACrB,IAAI,CAACK,GAAG,CAACC,UAAU,CAACL,IAAI,CAAC,EAAE;MACzBI,GAAG,CAACE,UAAU,CAACN,IAAI,EAAEC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,QAAQ,CAAC;MACpC,IAAIL,KAAK,EAAE;QACTA,KAAK,CAACH,OAAO,CAAC;;;IAIlB,OAAO,MAAK;MACV,IAAII,QAAQ,EAAE;QACZA,QAAQ,CAACJ,OAAO,CAAC;;MAEnB;MACA,IAAIK,GAAG,CAACC,UAAU,CAACL,IAAI,CAAC,EAAE;QACxBI,GAAG,CAACI,aAAa,CAACR,IAAI,CAAC;;IAE3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOA,IAAI;AACb;AAEA,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}