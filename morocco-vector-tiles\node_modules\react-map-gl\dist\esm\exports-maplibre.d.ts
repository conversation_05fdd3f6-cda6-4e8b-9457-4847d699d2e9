import * as React from 'react';
import type { Map as MaplibreMap, MapOptions, <PERSON><PERSON> as MaplibreMarker, <PERSON>er<PERSON><PERSON><PERSON>, Popup as MaplibrePopup, PopupOptions, AttributionControl as MaplibreAttributionControl, FullscreenControl as MaplibreFullscreenControl, GeolocateControl as MaplibreGeolocateControl, NavigationControl as MaplibreNavigationControl, ScaleControl as MaplibreScaleControl } from 'maplibre-gl';
import { MapStyle, AnyLayer, AnySource } from './types/style-spec-maplibre';
import { MapProps as _MapProps } from './components/map';
import { MarkerProps as _MarkerProps } from './components/marker';
import { PopupProps as _PopupProps } from './components/popup';
import { AttributionControlProps as _AttributionControlProps } from './components/attribution-control';
import { FullscreenControlProps as _FullscreenControlProps } from './components/fullscreen-control';
import { GeolocateControlProps as _GeolocateControlProps } from './components/geolocate-control';
import { NavigationControlProps as _NavigationControlProps } from './components/navigation-control';
import { ScaleControlProps as _ScaleControlProps } from './components/scale-control';
import { LayerProps as _LayerProps } from './components/layer';
import { SourceProps as _SourceProps } from './components/source';
import type { MapRef as _MapRef } from './mapbox/create-ref';
import type * as events from './types/events';
import type { MapCallbacks } from './types/events-maplibre';
export declare function useMap(): import("./components/use-map").MapCollection<MaplibreMap>;
export declare type MapProps = _MapProps<MapOptions, MapStyle, MapCallbacks, MaplibreMap>;
export declare type MapRef = _MapRef<MaplibreMap>;
export declare const Map: React.ForwardRefExoticComponent<{
    zoom?: number;
    bearing?: number;
    pitch?: number;
    maxZoom?: number;
    minZoom?: number;
    interactive?: boolean;
    minPitch?: number;
    maxPitch?: number;
    maxBounds?: import("maplibre-gl").LngLatBoundsLike;
    renderWorldCopies?: boolean;
    scrollZoom?: boolean;
    boxZoom?: boolean;
    dragRotate?: boolean;
    dragPan?: boolean | import("maplibre-gl").DragPanOptions;
    keyboard?: boolean;
    doubleClickZoom?: boolean;
    touchZoomRotate?: boolean;
    touchPitch?: boolean;
    localIdeographFontFamily?: string;
    pixelRatio?: number;
    antialias?: boolean;
    attributionControl?: boolean;
    bearingSnap?: number;
    clickTolerance?: number;
    collectResourceTiming?: boolean;
    crossSourceCollisions?: boolean;
    cooperativeGestures?: boolean | import("maplibre-gl").GestureOptions;
    customAttribution?: string | string[];
    hash?: string | boolean;
    fadeDuration?: number;
    failIfMajorPerformanceCaveat?: boolean;
    locale?: any;
    logoPosition?: import("maplibre-gl").ControlPosition;
    preserveDrawingBuffer?: boolean;
    pitchWithRotate?: boolean;
    refreshExpiredTiles?: boolean;
    trackResize?: boolean;
    transformRequest?: import("maplibre-gl").RequestTransformFunction;
    maxTileCacheSize?: number;
    maplibreLogo?: boolean;
} & Partial<import("./types/common").ViewState> & MapCallbacks & {
    mapboxAccessToken?: string;
    initialViewState?: Partial<import("./types/common").ViewState> & {
        bounds?: import("./types").LngLatBoundsLike;
        fitBoundsOptions?: {
            offset?: import("./types").PointLike;
            minZoom?: number;
            maxZoom?: number;
            padding?: number | import("./types").PaddingOptions;
        };
    };
    gl?: WebGLRenderingContext;
    viewState?: import("./types/common").ViewState & {
        width: number;
        height: number;
    };
    mapStyle?: string | MapStyle | import("./types").ImmutableLike<MapStyle>;
    styleDiffing?: boolean;
    fog?: unknown;
    light?: import("@maplibre/maplibre-gl-style-spec").LightSpecification;
    terrain?: import("@maplibre/maplibre-gl-style-spec").TerrainSpecification;
    interactiveLayerIds?: string[];
    cursor?: string;
} & import("./utils/set-globals").GlobalSettings & {
    mapLib?: import("./types/lib").MapLib<MaplibreMap> | Promise<import("./types/lib").MapLib<MaplibreMap>>;
    reuseMaps?: boolean;
    id?: string;
    style?: React.CSSProperties;
    children?: any;
} & React.RefAttributes<MapRef>>;
export declare type MarkerProps = _MarkerProps<MarkerOptions, MaplibreMarker>;
export declare const Marker: (props: MarkerProps & React.RefAttributes<MaplibreMarker>) => React.ReactElement | null;
export declare type PopupProps = _PopupProps<PopupOptions, MaplibrePopup>;
export declare const Popup: (props: PopupProps & React.RefAttributes<MaplibrePopup>) => React.ReactElement | null;
declare type AttributionControlOptions = ConstructorParameters<typeof MaplibreAttributionControl>[0];
export declare type AttributionControlProps = _AttributionControlProps<AttributionControlOptions>;
export declare const AttributionControl: (props: AttributionControlProps) => React.ReactElement | null;
declare type FullscreenControlOptions = ConstructorParameters<typeof MaplibreFullscreenControl>[0];
export declare type FullscreenControlProps = _FullscreenControlProps<FullscreenControlOptions>;
export declare const FullscreenControl: (props: FullscreenControlProps) => React.ReactElement | null;
declare type NavigationControlOptions = ConstructorParameters<typeof MaplibreNavigationControl>[0];
export declare type NavigationControlProps = _NavigationControlProps<NavigationControlOptions>;
export declare const NavigationControl: (props: NavigationControlProps) => React.ReactElement | null;
declare type GeolocateControlOptions = ConstructorParameters<typeof MaplibreGeolocateControl>[0];
export declare type GeolocateControlProps = _GeolocateControlProps<GeolocateControlOptions, MaplibreGeolocateControl>;
export declare const GeolocateControl: (props: GeolocateControlProps & React.RefAttributes<MaplibreGeolocateControl>) => React.ReactElement | null;
declare type ScaleControlOptions = ConstructorParameters<typeof MaplibreScaleControl>[0];
export declare type ScaleControlProps = _ScaleControlProps<ScaleControlOptions>;
export declare const ScaleControl: (props: ScaleControlProps) => React.ReactElement | null;
export declare type LayerProps = _LayerProps<AnyLayer>;
export declare const Layer: (props: LayerProps) => React.ReactElement | null;
export declare type SourceProps = _SourceProps<AnySource>;
export declare const Source: (props: SourceProps) => React.ReactElement | null;
export { default as useControl } from './components/use-control';
export { MapProvider } from './components/use-map';
export default Map;
export * from './types/public';
export type { default as Point } from '@mapbox/point-geometry';
export type { PointLike, LngLat, LngLatLike, LngLatBounds, LngLatBoundsLike, PaddingOptions, MapGeoJSONFeature, GeoJSONSource, VideoSource, ImageSource, CanvasSource, VectorTileSource } from 'maplibre-gl';
export * from './types/style-spec-maplibre';
export type { MapEvent, MapMouseEvent, MapLayerMouseEvent, MapTouchEvent, MapLayerTouchEvent, MapStyleDataEvent, MapSourceDataEvent, MapWheelEvent, MapBoxZoomEvent, ErrorEvent, ViewStateChangeEvent } from './types/events-maplibre';
export declare type PopupEvent = events.PopupEvent<MaplibrePopup>;
export declare type MarkerEvent = events.MarkerEvent<MaplibreMarker>;
export declare type MarkerDragEvent = events.MarkerDragEvent<MaplibreMarker>;
export declare type GeolocateEvent = events.GeolocateEvent<MaplibreGeolocateControl>;
export declare type GeolocateResultEvent = events.GeolocateResultEvent<MaplibreGeolocateControl>;
export declare type GeolocateErrorEvent = events.GeolocateErrorEvent<MaplibreGeolocateControl>;
