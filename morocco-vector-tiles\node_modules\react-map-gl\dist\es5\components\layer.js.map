{"version": 3, "file": "layer.js", "sourceRoot": "", "sources": ["../../../src/components/layer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+BAAuE;AACvE,6BAAiC;AACjC,0CAAqC;AACrC,kDAA8C;AAc9C,+CAA+C;AAC/C,SAAS,WAAW,CAClB,GAAgB,EAChB,EAAU,EACV,KAAyB,EACzB,SAA6B;IAE7B,IAAA,gBAAM,EAAC,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;IACtD,IAAA,gBAAM,EAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;IAE5D,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC1D,OAAO;KACR;IAEM,IAAA,KAA+D,KAAK,OAAzD,EAAX,MAAM,mBAAG,EAAE,KAAA,EAAE,KAAkD,KAAK,MAA7C,EAAV,KAAK,mBAAG,EAAE,KAAA,EAAE,MAAM,GAAgC,KAAK,OAArC,EAAE,OAAO,GAAuB,KAAK,QAA5B,EAAE,OAAO,GAAc,KAAK,QAAnB,EAAE,QAAQ,GAAI,KAAK,SAAT,CAAU;IAE5E,IAAI,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE;QACnC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;KAC7B;IACD,IAAI,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE;QAC/B,IAAM,UAAU,GAAG,SAAS,CAAC,MAAM,IAAI,EAAE,CAAC;QAC1C,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;YACxB,IAAI,CAAC,IAAA,sBAAS,EAAC,MAAM,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC5C,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aAC7C;SACF;QACD,KAAK,IAAM,GAAG,IAAI,UAAU,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBAC/B,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;aAC3C;SACF;KACF;IACD,IAAI,KAAK,KAAK,SAAS,CAAC,KAAK,EAAE;QAC7B,IAAM,SAAS,GAAG,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC;QACxC,KAAK,IAAM,GAAG,IAAI,KAAK,EAAE;YACvB,IAAI,CAAC,IAAA,sBAAS,EAAC,KAAK,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC1C,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3C;SACF;QACD,KAAK,IAAM,GAAG,IAAI,SAAS,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBAC9B,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;aAC1C;SACF;KACF;IAED,IAAI,CAAC,IAAA,sBAAS,EAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE;QACxC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;KAC3B;IACD,IAAI,OAAO,KAAK,SAAS,CAAC,OAAO,IAAI,OAAO,KAAK,SAAS,CAAC,OAAO,EAAE;QAClE,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;KAC7C;AACH,CAAC;AAED,SAAS,WAAW,CAClB,GAAgB,EAChB,EAAU,EACV,KAAyB;IAEzB,aAAa;IACb,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE;QAC3F,IAAM,OAAO,yBAA2B,KAAK,KAAE,EAAE,IAAA,GAAC,CAAC;QACnD,OAAO,OAAO,CAAC,QAAQ,CAAC;QAExB,aAAa;QACb,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;KACvC;AACH,CAAC;AAED,8CAA8C;AAE9C,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB,SAAS,KAAK,CAAwB,KAAgD;IACpF,IAAM,GAAG,GAAG,IAAA,kBAAU,EAAC,gBAAU,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;IAChD,IAAM,QAAQ,GAAG,IAAA,cAAM,EAAC,KAAK,CAAC,CAAC;IACzB,IAAA,KAAA,OAAqB,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAA,EAA7B,cAAc,QAAe,CAAC;IAEvC,IAAM,EAAE,GAAG,IAAA,eAAO,EAAC,cAAM,OAAA,KAAK,CAAC,EAAE,IAAI,oBAAa,YAAY,EAAE,CAAE,EAAzC,CAAyC,EAAE,EAAE,CAAC,CAAC;IAExE,IAAA,iBAAS,EAAC;QACR,IAAI,GAAG,EAAE;YACP,IAAM,aAAW,GAAG,cAAM,OAAA,cAAc,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,GAAG,CAAC,EAAX,CAAW,CAAC,EAAtC,CAAsC,CAAC;YACjE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,aAAW,CAAC,CAAC;YACjC,aAAW,EAAE,CAAC;YAEd,OAAO;gBACL,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,aAAW,CAAC,CAAC;gBAClC,aAAa;gBACb,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;oBACtD,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;iBACrB;YACH,CAAC,CAAC;SACH;QACD,OAAO,SAAS,CAAC;IACnB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAEV,aAAa;IACb,IAAM,KAAK,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACnD,IAAI,KAAK,EAAE;QACT,IAAI;YACF,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;SAC/C;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,sBAAsB;SAC5C;KACF;SAAM;QACL,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;KAC7B;IAED,4BAA4B;IAC5B,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;IAEzB,OAAO,IAAI,CAAC;AACd,CAAC;AAED,kBAAe,KAAK,CAAC"}