{"ast": null, "code": "import * as React from 'react';\nimport { useContext, useEffect, useMemo, useState, useRef } from 'react';\nimport { cloneElement } from 'react';\nimport { MapContext } from './map';\nimport assert from '../utils/assert';\nimport { deepEqual } from '../utils/deep-equal';\nlet sourceCounter = 0;\nfunction createSource(map, id, props) {\n  // @ts-ignore\n  if (map.style && map.style._loaded) {\n    const options = {\n      ...props\n    };\n    delete options.id;\n    delete options.children;\n    // @ts-ignore\n    map.addSource(id, options);\n    return map.getSource(id);\n  }\n  return null;\n}\n/* eslint-disable complexity */\nfunction updateSource(source, props, prevProps) {\n  assert(props.id === prevProps.id, 'source id changed');\n  assert(props.type === prevProps.type, 'source type changed');\n  let changedKey = '';\n  let changedKeyCount = 0;\n  for (const key in props) {\n    if (key !== 'children' && key !== 'id' && !deepEqual(prevProps[key], props[key])) {\n      changedKey = key;\n      changedKeyCount++;\n    }\n  }\n  if (!changedKeyCount) {\n    return;\n  }\n  const type = props.type;\n  if (type === 'geojson') {\n    source.setData(props.data);\n  } else if (type === 'image') {\n    source.updateImage({\n      url: props.url,\n      coordinates: props.coordinates\n    });\n  } else if ('setCoordinates' in source && changedKeyCount === 1 && changedKey === 'coordinates') {\n    source.setCoordinates(props.coordinates);\n  } else if ('setUrl' in source) {\n    // Added in 1.12.0:\n    // vectorTileSource.setTiles\n    // vectorTileSource.setUrl\n    switch (changedKey) {\n      case 'url':\n        source.setUrl(props.url);\n        break;\n      case 'tiles':\n        source.setTiles(props.tiles);\n        break;\n      default:\n    }\n  } else {\n    // eslint-disable-next-line\n    console.warn(`Unable to update <Source> prop: ${changedKey}`);\n  }\n}\n/* eslint-enable complexity */\nfunction Source(props) {\n  const map = useContext(MapContext).map.getMap();\n  const propsRef = useRef(props);\n  const [, setStyleLoaded] = useState(0);\n  const id = useMemo(() => props.id || `jsx-source-${sourceCounter++}`, []);\n  useEffect(() => {\n    if (map) {\n      /* global setTimeout */\n      const forceUpdate = () => setTimeout(() => setStyleLoaded(version => version + 1), 0);\n      map.on('styledata', forceUpdate);\n      forceUpdate();\n      return () => {\n        var _a;\n        map.off('styledata', forceUpdate);\n        // @ts-ignore\n        if (map.style && map.style._loaded && map.getSource(id)) {\n          // Parent effects are destroyed before child ones, see\n          // https://github.com/facebook/react/issues/16728\n          // Source can only be removed after all child layers are removed\n          const allLayers = (_a = map.getStyle()) === null || _a === void 0 ? void 0 : _a.layers;\n          if (allLayers) {\n            for (const layer of allLayers) {\n              // @ts-ignore (2339) source does not exist on all layer types\n              if (layer.source === id) {\n                map.removeLayer(layer.id);\n              }\n            }\n          }\n          map.removeSource(id);\n        }\n      };\n    }\n    return undefined;\n  }, [map]);\n  // @ts-ignore\n  let source = map && map.style && map.getSource(id);\n  if (source) {\n    updateSource(source, props, propsRef.current);\n  } else {\n    source = createSource(map, id, props);\n  }\n  propsRef.current = props;\n  return source && React.Children.map(props.children, child => child && cloneElement(child, {\n    source: id\n  })) || null;\n}\nexport default Source;", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useMemo", "useState", "useRef", "cloneElement", "MapContext", "assert", "deepEqual", "sourceCounter", "createSource", "map", "id", "props", "style", "_loaded", "options", "children", "addSource", "getSource", "updateSource", "source", "prevProps", "type", "<PERSON><PERSON><PERSON>", "changedKeyCount", "key", "setData", "data", "updateImage", "url", "coordinates", "setCoordinates", "setUrl", "setTiles", "tiles", "console", "warn", "Source", "getMap", "propsRef", "setStyleLoaded", "forceUpdate", "setTimeout", "version", "on", "off", "allLayers", "_a", "getStyle", "layers", "layer", "<PERSON><PERSON><PERSON>er", "removeSource", "undefined", "current", "Children", "child"], "sources": ["../../../src/components/source.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAQC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,QAAO,OAAO;AACtE,SAAQC,YAAY,QAAO,OAAO;AAClC,SAAQC,UAAU,QAAO,OAAO;AAChC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,SAAQC,SAAS,QAAO,qBAAqB;AAiB7C,IAAIC,aAAa,GAAG,CAAC;AAErB,SAASC,YAAYA,CACnBC,GAAgB,EAChBC,EAAU,EACVC,KAA2B;EAE3B;EACA,IAAIF,GAAG,CAACG,KAAK,IAAIH,GAAG,CAACG,KAAK,CAACC,OAAO,EAAE;IAClC,MAAMC,OAAO,GAAG;MAAC,GAAGH;IAAK,CAAC;IAC1B,OAAOG,OAAO,CAACJ,EAAE;IACjB,OAAOI,OAAO,CAACC,QAAQ;IACvB;IACAN,GAAG,CAACO,SAAS,CAACN,EAAE,EAAEI,OAAO,CAAC;IAC1B,OAAOL,GAAG,CAACQ,SAAS,CAACP,EAAE,CAAC;;EAE1B,OAAO,IAAI;AACb;AAEA;AACA,SAASQ,YAAYA,CACnBC,MAA+B,EAC/BR,KAA2B,EAC3BS,SAA+B;EAE/Bf,MAAM,CAACM,KAAK,CAACD,EAAE,KAAKU,SAAS,CAACV,EAAE,EAAE,mBAAmB,CAAC;EACtDL,MAAM,CAACM,KAAK,CAACU,IAAI,KAAKD,SAAS,CAACC,IAAI,EAAE,qBAAqB,CAAC;EAE5D,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,eAAe,GAAG,CAAC;EAEvB,KAAK,MAAMC,GAAG,IAAIb,KAAK,EAAE;IACvB,IAAIa,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,IAAI,IAAI,CAAClB,SAAS,CAACc,SAAS,CAACI,GAAG,CAAC,EAAEb,KAAK,CAACa,GAAG,CAAC,CAAC,EAAE;MAChFF,UAAU,GAAGE,GAAG;MAChBD,eAAe,EAAE;;;EAIrB,IAAI,CAACA,eAAe,EAAE;IACpB;;EAGF,MAAMF,IAAI,GAAGV,KAAK,CAACU,IAAI;EAEvB,IAAIA,IAAI,KAAK,SAAS,EAAE;IACrBF,MAAsC,CAACM,OAAO,CAC5Cd,KAAqC,CAACe,IAAW,CACnD;GACF,MAAM,IAAIL,IAAI,KAAK,OAAO,EAAE;IAC1BF,MAAkC,CAACQ,WAAW,CAAC;MAC9CC,GAAG,EAAGjB,KAAmC,CAACiB,GAAG;MAC7CC,WAAW,EAAGlB,KAAmC,CAACkB;KACnD,CAAC;GACH,MAAM,IAAI,gBAAgB,IAAIV,MAAM,IAAII,eAAe,KAAK,CAAC,IAAID,UAAU,KAAK,aAAa,EAAE;IAC9FH,MAAM,CAACW,cAAc,CAAEnB,KAAwB,CAACkB,WAAW,CAAC;GAC7D,MAAM,IAAI,QAAQ,IAAIV,MAAM,EAAE;IAC7B;IACA;IACA;IACA,QAAQG,UAAU;MAChB,KAAK,KAAK;QACRH,MAAM,CAACY,MAAM,CAAEpB,KAAyB,CAACiB,GAAG,CAAC;QAC7C;MACF,KAAK,OAAO;QACVT,MAAM,CAACa,QAAQ,CAAErB,KAAyB,CAACsB,KAAK,CAAC;QACjD;MACF;;GAEH,MAAM;IACL;IACAC,OAAO,CAACC,IAAI,CAAC,mCAAmCb,UAAU,EAAE,CAAC;;AAEjE;AACA;AAEA,SAASc,MAAMA,CAA0BzB,KAA2B;EAClE,MAAMF,GAAG,GAAGX,UAAU,CAACM,UAAU,CAAC,CAACK,GAAG,CAAC4B,MAAM,EAAE;EAC/C,MAAMC,QAAQ,GAAGpC,MAAM,CAACS,KAAK,CAAC;EAC9B,MAAM,GAAG4B,cAAc,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EAEtC,MAAMS,EAAE,GAAGV,OAAO,CAAC,MAAMW,KAAK,CAACD,EAAE,IAAI,cAAcH,aAAa,EAAE,EAAE,EAAE,EAAE,CAAC;EAEzER,SAAS,CAAC,MAAK;IACb,IAAIU,GAAG,EAAE;MACP;MACA,MAAM+B,WAAW,GAAGA,CAAA,KAAMC,UAAU,CAAC,MAAMF,cAAc,CAACG,OAAO,IAAIA,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACrFjC,GAAG,CAACkC,EAAE,CAAC,WAAW,EAAEH,WAAW,CAAC;MAChCA,WAAW,EAAE;MAEb,OAAO,MAAK;;QACV/B,GAAG,CAACmC,GAAG,CAAC,WAAW,EAAEJ,WAAW,CAAC;QACjC;QACA,IAAI/B,GAAG,CAACG,KAAK,IAAIH,GAAG,CAACG,KAAK,CAACC,OAAO,IAAIJ,GAAG,CAACQ,SAAS,CAACP,EAAE,CAAC,EAAE;UACvD;UACA;UACA;UACA,MAAMmC,SAAS,GAAG,CAAAC,EAAA,GAAArC,GAAG,CAACsC,QAAQ,EAAE,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,MAAM;UACxC,IAAIH,SAAS,EAAE;YACb,KAAK,MAAMI,KAAK,IAAIJ,SAAS,EAAE;cAC7B;cACA,IAAII,KAAK,CAAC9B,MAAM,KAAKT,EAAE,EAAE;gBACvBD,GAAG,CAACyC,WAAW,CAACD,KAAK,CAACvC,EAAE,CAAC;;;;UAI/BD,GAAG,CAAC0C,YAAY,CAACzC,EAAE,CAAC;;MAExB,CAAC;;IAEH,OAAO0C,SAAS;EAClB,CAAC,EAAE,CAAC3C,GAAG,CAAC,CAAC;EAET;EACA,IAAIU,MAAM,GAAGV,GAAG,IAAIA,GAAG,CAACG,KAAK,IAAIH,GAAG,CAACQ,SAAS,CAACP,EAAE,CAAC;EAClD,IAAIS,MAAM,EAAE;IACVD,YAAY,CAACC,MAAM,EAAER,KAAK,EAAE2B,QAAQ,CAACe,OAAO,CAAC;GAC9C,MAAM;IACLlC,MAAM,GAAGX,YAAY,CAACC,GAAG,EAAEC,EAAE,EAAEC,KAAK,CAAC;;EAEvC2B,QAAQ,CAACe,OAAO,GAAG1C,KAAK;EAExB,OACGQ,MAAM,IACLtB,KAAK,CAACyD,QAAQ,CAAC7C,GAAG,CAChBE,KAAK,CAACI,QAAQ,EACdwC,KAAK,IACHA,KAAK,IACLpD,YAAY,CAACoD,KAAK,EAAE;IAClBpC,MAAM,EAAET;GACT,CAAC,CACL,IACH,IAAI;AAER;AAEA,eAAe0B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}