{"ast": null, "code": "import { createPortal } from 'react-dom';\nimport { useImperative<PERSON><PERSON>le, useEffect, useMemo, useRef, useContext, forwardRef, memo } from 'react';\nimport { applyReactStyle } from '../utils/apply-react-style';\nimport { MapContext } from './map';\nimport { deepEqual } from '../utils/deep-equal';\n// Adapted from https://github.com/mapbox/mapbox-gl-js/blob/v1.13.0/src/ui/popup.js\nfunction getClassList(className) {\n  return new Set(className ? className.trim().split(/\\s+/) : []);\n}\n/* eslint-disable complexity,max-statements */\nfunction Popup(props, ref) {\n  const {\n    map,\n    mapLib\n  } = useContext(MapContext);\n  const container = useMemo(() => {\n    return document.createElement('div');\n  }, []);\n  const thisRef = useRef({\n    props\n  });\n  thisRef.current.props = props;\n  const popup = useMemo(() => {\n    const options = {\n      ...props\n    };\n    const pp = new mapLib.Popup(options);\n    pp.setLngLat([props.longitude, props.latitude]);\n    pp.once('open', e => {\n      var _a, _b;\n      (_b = (_a = thisRef.current.props).onOpen) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n    });\n    return pp;\n  }, []);\n  useEffect(() => {\n    const onClose = e => {\n      var _a, _b;\n      (_b = (_a = thisRef.current.props).onClose) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n    };\n    popup.on('close', onClose);\n    popup.setDOMContent(container).addTo(map.getMap());\n    return () => {\n      // https://github.com/visgl/react-map-gl/issues/1825\n      // onClose should not be fired if the popup is removed by unmounting\n      // When using React strict mode, the component is mounted twice.\n      // Firing the onClose callback here would be a false signal to remove the component.\n      popup.off('close', onClose);\n      if (popup.isOpen()) {\n        popup.remove();\n      }\n    };\n  }, []);\n  useEffect(() => {\n    applyReactStyle(popup.getElement(), props.style);\n  }, [props.style]);\n  useImperativeHandle(ref, () => popup, []);\n  if (popup.isOpen()) {\n    if (popup.getLngLat().lng !== props.longitude || popup.getLngLat().lat !== props.latitude) {\n      popup.setLngLat([props.longitude, props.latitude]);\n    }\n    if (props.offset && !deepEqual(popup.options.offset, props.offset)) {\n      popup.setOffset(props.offset);\n    }\n    if (popup.options.anchor !== props.anchor || popup.options.maxWidth !== props.maxWidth) {\n      popup.options.anchor = props.anchor;\n      popup.setMaxWidth(props.maxWidth);\n    }\n    if (popup.options.className !== props.className) {\n      const prevClassList = getClassList(popup.options.className);\n      const nextClassList = getClassList(props.className);\n      for (const c of prevClassList) {\n        if (!nextClassList.has(c)) {\n          popup.removeClassName(c);\n        }\n      }\n      for (const c of nextClassList) {\n        if (!prevClassList.has(c)) {\n          popup.addClassName(c);\n        }\n      }\n      popup.options.className = props.className;\n    }\n  }\n  return createPortal(props.children, container);\n}\nexport default memo(forwardRef(Popup));", "map": {"version": 3, "names": ["createPortal", "useImperativeHandle", "useEffect", "useMemo", "useRef", "useContext", "forwardRef", "memo", "applyReactStyle", "MapContext", "deepEqual", "getClassList", "className", "Set", "trim", "split", "Popup", "props", "ref", "map", "mapLib", "container", "document", "createElement", "thisRef", "current", "popup", "options", "pp", "setLngLat", "longitude", "latitude", "once", "e", "_b", "_a", "onOpen", "call", "onClose", "on", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "addTo", "getMap", "off", "isOpen", "remove", "getElement", "style", "getLngLat", "lng", "lat", "offset", "setOffset", "anchor", "max<PERSON><PERSON><PERSON>", "setMaxWidth", "prevClassList", "nextClassList", "c", "has", "removeClassName", "addClassName", "children"], "sources": ["../../../src/components/popup.ts"], "sourcesContent": [null], "mappings": "AAEA,SAAQA,YAAY,QAAO,WAAW;AACtC,SAAQC,mBAAmB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,IAAI,QAAO,OAAO;AACnG,SAAQC,eAAe,QAAO,4BAA4B;AAI1D,SAAQC,UAAU,QAAO,OAAO;AAChC,SAAQC,SAAS,QAAO,qBAAqB;AAsB7C;AACA,SAASC,YAAYA,CAACC,SAAiB;EACrC,OAAO,IAAIC,GAAG,CAACD,SAAS,GAAGA,SAAS,CAACE,IAAI,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAChE;AAEA;AACA,SAASC,KAAKA,CACZC,KAAuC,EACvCC,GAAsB;EAEtB,MAAM;IAACC,GAAG;IAAEC;EAAM,CAAC,GAAGf,UAAU,CAACI,UAAU,CAAC;EAC5C,MAAMY,SAAS,GAAGlB,OAAO,CAAC,MAAK;IAC7B,OAAOmB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,OAAO,GAAGpB,MAAM,CAAC;IAACa;EAAK,CAAC,CAAC;EAC/BO,OAAO,CAACC,OAAO,CAACR,KAAK,GAAGA,KAAK;EAE7B,MAAMS,KAAK,GAAWvB,OAAO,CAAC,MAAK;IACjC,MAAMwB,OAAO,GAAG;MAAC,GAAGV;IAAK,CAAC;IAC1B,MAAMW,EAAE,GAAG,IAAIR,MAAM,CAACJ,KAAK,CAACW,OAAO,CAAW;IAC9CC,EAAE,CAACC,SAAS,CAAC,CAACZ,KAAK,CAACa,SAAS,EAAEb,KAAK,CAACc,QAAQ,CAAC,CAAC;IAC/CH,EAAE,CAACI,IAAI,CAAC,MAAM,EAAEC,CAAC,IAAG;;MAClB,CAAAC,EAAA,IAAAC,EAAA,GAAAX,OAAO,CAACC,OAAO,CAACR,KAAK,EAACmB,MAAM,cAAAF,EAAA,uBAAAA,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAGF,CAAuB,CAAC;IACzD,CAAC,CAAC;IACF,OAAOL,EAAE;EACX,CAAC,EAAE,EAAE,CAAC;EAEN1B,SAAS,CAAC,MAAK;IACb,MAAMoC,OAAO,GAAGL,CAAC,IAAG;;MAClB,CAAAC,EAAA,IAAAC,EAAA,GAAAX,OAAO,CAACC,OAAO,CAACR,KAAK,EAACqB,OAAO,cAAAJ,EAAA,uBAAAA,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAGF,CAAuB,CAAC;IAC1D,CAAC;IACDP,KAAK,CAACa,EAAE,CAAC,OAAO,EAAED,OAAO,CAAC;IAC1BZ,KAAK,CAACc,aAAa,CAACnB,SAAS,CAAC,CAACoB,KAAK,CAACtB,GAAG,CAACuB,MAAM,EAAE,CAAC;IAElD,OAAO,MAAK;MACV;MACA;MACA;MACA;MACAhB,KAAK,CAACiB,GAAG,CAAC,OAAO,EAAEL,OAAO,CAAC;MAC3B,IAAIZ,KAAK,CAACkB,MAAM,EAAE,EAAE;QAClBlB,KAAK,CAACmB,MAAM,EAAE;;IAElB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN3C,SAAS,CAAC,MAAK;IACbM,eAAe,CAACkB,KAAK,CAACoB,UAAU,EAAE,EAAE7B,KAAK,CAAC8B,KAAK,CAAC;EAClD,CAAC,EAAE,CAAC9B,KAAK,CAAC8B,KAAK,CAAC,CAAC;EAEjB9C,mBAAmB,CAACiB,GAAG,EAAE,MAAMQ,KAAK,EAAE,EAAE,CAAC;EAEzC,IAAIA,KAAK,CAACkB,MAAM,EAAE,EAAE;IAClB,IAAIlB,KAAK,CAACsB,SAAS,EAAE,CAACC,GAAG,KAAKhC,KAAK,CAACa,SAAS,IAAIJ,KAAK,CAACsB,SAAS,EAAE,CAACE,GAAG,KAAKjC,KAAK,CAACc,QAAQ,EAAE;MACzFL,KAAK,CAACG,SAAS,CAAC,CAACZ,KAAK,CAACa,SAAS,EAAEb,KAAK,CAACc,QAAQ,CAAC,CAAC;;IAEpD,IAAId,KAAK,CAACkC,MAAM,IAAI,CAACzC,SAAS,CAACgB,KAAK,CAACC,OAAO,CAACwB,MAAM,EAAElC,KAAK,CAACkC,MAAM,CAAC,EAAE;MAClEzB,KAAK,CAAC0B,SAAS,CAACnC,KAAK,CAACkC,MAAM,CAAC;;IAE/B,IAAIzB,KAAK,CAACC,OAAO,CAAC0B,MAAM,KAAKpC,KAAK,CAACoC,MAAM,IAAI3B,KAAK,CAACC,OAAO,CAAC2B,QAAQ,KAAKrC,KAAK,CAACqC,QAAQ,EAAE;MACtF5B,KAAK,CAACC,OAAO,CAAC0B,MAAM,GAAGpC,KAAK,CAACoC,MAAM;MACnC3B,KAAK,CAAC6B,WAAW,CAACtC,KAAK,CAACqC,QAAQ,CAAC;;IAEnC,IAAI5B,KAAK,CAACC,OAAO,CAACf,SAAS,KAAKK,KAAK,CAACL,SAAS,EAAE;MAC/C,MAAM4C,aAAa,GAAG7C,YAAY,CAACe,KAAK,CAACC,OAAO,CAACf,SAAS,CAAC;MAC3D,MAAM6C,aAAa,GAAG9C,YAAY,CAACM,KAAK,CAACL,SAAS,CAAC;MAEnD,KAAK,MAAM8C,CAAC,IAAIF,aAAa,EAAE;QAC7B,IAAI,CAACC,aAAa,CAACE,GAAG,CAACD,CAAC,CAAC,EAAE;UACzBhC,KAAK,CAACkC,eAAe,CAACF,CAAC,CAAC;;;MAG5B,KAAK,MAAMA,CAAC,IAAID,aAAa,EAAE;QAC7B,IAAI,CAACD,aAAa,CAACG,GAAG,CAACD,CAAC,CAAC,EAAE;UACzBhC,KAAK,CAACmC,YAAY,CAACH,CAAC,CAAC;;;MAGzBhC,KAAK,CAACC,OAAO,CAACf,SAAS,GAAGK,KAAK,CAACL,SAAS;;;EAI7C,OAAOZ,YAAY,CAACiB,KAAK,CAAC6C,QAAQ,EAAEzC,SAAS,CAAC;AAChD;AAEA,eAAed,IAAI,CAACD,UAAU,CAACU,KAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}