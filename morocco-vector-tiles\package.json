{"name": "morocco-vector-tiles", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@turf/turf": "^7.2.0", "mapbox-gl": "^3.13.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-map-gl": "^7.1.7", "react-scripts": "5.0.1", "tailwindcss": "^4.1.11", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}