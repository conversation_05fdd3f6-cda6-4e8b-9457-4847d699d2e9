{"ast": null, "code": "import { useEffect, memo } from 'react';\nimport { applyReactStyle } from '../utils/apply-react-style';\nimport useControl from './use-control';\nfunction NavigationControl(props) {\n  const ctrl = useControl(({\n    mapLib\n  }) => new mapLib.NavigationControl(props), {\n    position: props.position\n  });\n  useEffect(() => {\n    applyReactStyle(ctrl._container, props.style);\n  }, [props.style]);\n  return null;\n}\nexport default memo(NavigationControl);", "map": {"version": 3, "names": ["useEffect", "memo", "applyReactStyle", "useControl", "NavigationControl", "props", "ctrl", "mapLib", "position", "_container", "style"], "sources": ["../../../src/components/navigation-control.ts"], "sourcesContent": [null], "mappings": "AACA,SAAQA,SAAS,EAAEC,IAAI,QAAO,OAAO;AACrC,SAAQC,eAAe,QAAO,4BAA4B;AAC1D,OAAOC,UAAU,MAAM,eAAe;AAWtC,SAASC,iBAAiBA,CACxBC,KAAuD;EAEvD,MAAMC,IAAI,GAAGH,UAAU,CAAW,CAAC;IAACI;EAAM,CAAC,KAAK,IAAIA,MAAM,CAACH,iBAAiB,CAACC,KAAK,CAAa,EAAE;IAC/FG,QAAQ,EAAEH,KAAK,CAACG;GACjB,CAAC;EAEFR,SAAS,CAAC,MAAK;IACbE,eAAe,CAACI,IAAI,CAACG,UAAU,EAAEJ,KAAK,CAACK,KAAK,CAAC;EAC/C,CAAC,EAAE,CAACL,KAAK,CAACK,KAAK,CAAC,CAAC;EAEjB,OAAO,IAAI;AACb;AAEA,eAAeT,IAAI,CAACG,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}