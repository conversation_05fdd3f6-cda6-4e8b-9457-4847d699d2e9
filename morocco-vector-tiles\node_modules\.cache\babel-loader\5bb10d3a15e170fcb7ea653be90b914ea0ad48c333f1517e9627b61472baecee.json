{"ast": null, "code": "import { transformToViewState, applyViewStateToTransform, cloneTransform, syncProjection } from '../utils/transform';\nimport { normalizeStyle } from '../utils/style-utils';\nimport { deepEqual } from '../utils/deep-equal';\nconst DEFAULT_STYLE = {\n  version: 8,\n  sources: {},\n  layers: []\n};\nconst pointerEvents = {\n  mousedown: 'onMouseDown',\n  mouseup: 'onMouseUp',\n  mouseover: 'onMouseOver',\n  mousemove: 'onMouseMove',\n  click: 'onClick',\n  dblclick: 'onDblClick',\n  mouseenter: 'onMouseEnter',\n  mouseleave: 'onMouseLeave',\n  mouseout: 'onMouseOut',\n  contextmenu: 'onContextMenu',\n  touchstart: 'onTouchStart',\n  touchend: 'onTouchEnd',\n  touchmove: 'onTouchMove',\n  touchcancel: 'onTouchCancel'\n};\nconst cameraEvents = {\n  movestart: 'onMoveStart',\n  move: 'onMove',\n  moveend: 'onMoveEnd',\n  dragstart: 'onDragStart',\n  drag: 'onDrag',\n  dragend: 'onDragEnd',\n  zoomstart: 'onZoomStart',\n  zoom: 'onZoom',\n  zoomend: 'onZoomEnd',\n  rotatestart: 'onRotateStart',\n  rotate: 'onRotate',\n  rotateend: 'onRotateEnd',\n  pitchstart: 'onPitchStart',\n  pitch: 'onPitch',\n  pitchend: 'onPitchEnd'\n};\nconst otherEvents = {\n  wheel: 'onWheel',\n  boxzoomstart: 'onBoxZoomStart',\n  boxzoomend: 'onBoxZoomEnd',\n  boxzoomcancel: 'onBoxZoomCancel',\n  resize: 'onResize',\n  load: 'onLoad',\n  render: 'onRender',\n  idle: 'onIdle',\n  remove: 'onRemove',\n  data: 'onData',\n  styledata: 'onStyleData',\n  sourcedata: 'onSourceData',\n  error: 'onError'\n};\nconst settingNames = ['minZoom', 'maxZoom', 'minPitch', 'maxPitch', 'maxBounds', 'projection', 'renderWorldCopies'];\nconst handlerNames = ['scrollZoom', 'boxZoom', 'dragRotate', 'dragPan', 'keyboard', 'doubleClickZoom', 'touchZoomRotate', 'touchPitch'];\n/**\n * A wrapper for mapbox-gl's Map class\n */\nexport default class Mapbox {\n  constructor(MapClass, props, container) {\n    // mapboxgl.Map instance\n    this._map = null;\n    // Internal states\n    this._internalUpdate = false;\n    this._inRender = false;\n    this._hoveredFeatures = null;\n    this._deferredEvents = {\n      move: false,\n      zoom: false,\n      pitch: false,\n      rotate: false\n    };\n    this._onEvent = e => {\n      // @ts-ignore\n      const cb = this.props[otherEvents[e.type]];\n      if (cb) {\n        cb(e);\n      } else if (e.type === 'error') {\n        console.error(e.error); // eslint-disable-line\n      }\n    };\n    this._onPointerEvent = e => {\n      if (e.type === 'mousemove' || e.type === 'mouseout') {\n        this._updateHover(e);\n      }\n      // @ts-ignore\n      const cb = this.props[pointerEvents[e.type]];\n      if (cb) {\n        if (this.props.interactiveLayerIds && e.type !== 'mouseover' && e.type !== 'mouseout') {\n          e.features = this._hoveredFeatures || this._queryRenderedFeatures(e.point);\n        }\n        cb(e);\n        delete e.features;\n      }\n    };\n    this._onCameraEvent = e => {\n      if (!this._internalUpdate) {\n        // @ts-ignore\n        const cb = this.props[cameraEvents[e.type]];\n        if (cb) {\n          cb(e);\n        }\n      }\n      if (e.type in this._deferredEvents) {\n        this._deferredEvents[e.type] = false;\n      }\n    };\n    this._MapClass = MapClass;\n    this.props = props;\n    this._initialize(container);\n  }\n  get map() {\n    return this._map;\n  }\n  get transform() {\n    return this._renderTransform;\n  }\n  setProps(props) {\n    const oldProps = this.props;\n    this.props = props;\n    const settingsChanged = this._updateSettings(props, oldProps);\n    if (settingsChanged) {\n      this._createShadowTransform(this._map);\n    }\n    const sizeChanged = this._updateSize(props);\n    const viewStateChanged = this._updateViewState(props, true);\n    this._updateStyle(props, oldProps);\n    this._updateStyleComponents(props, oldProps);\n    this._updateHandlers(props, oldProps);\n    // If 1) view state has changed to match props and\n    //    2) the props change is not triggered by map events,\n    // it's driven by an external state change. Redraw immediately\n    if (settingsChanged || sizeChanged || viewStateChanged && !this._map.isMoving()) {\n      this.redraw();\n    }\n  }\n  static reuse(props, container) {\n    const that = Mapbox.savedMaps.pop();\n    if (!that) {\n      return null;\n    }\n    const map = that.map;\n    // When reusing the saved map, we need to reparent the map(canvas) and other child nodes\n    // intoto the new container from the props.\n    // Step 1: reparenting child nodes from old container to new container\n    const oldContainer = map.getContainer();\n    container.className = oldContainer.className;\n    while (oldContainer.childNodes.length > 0) {\n      container.appendChild(oldContainer.childNodes[0]);\n    }\n    // Step 2: replace the internal container with new container from the react component\n    // @ts-ignore\n    map._container = container;\n    // With maplibre-gl as mapLib, map uses ResizeObserver to observe when its container resizes.\n    // When reusing the saved map, we need to disconnect the observer and observe the new container.\n    // Step 3: telling the ResizeObserver to disconnect and observe the new container\n    // @ts-ignore\n    const resizeObserver = map._resizeObserver;\n    if (resizeObserver) {\n      resizeObserver.disconnect();\n      resizeObserver.observe(container);\n    }\n    // Step 4: apply new props\n    that.setProps({\n      ...props,\n      styleDiffing: false\n    });\n    map.resize();\n    const {\n      initialViewState\n    } = props;\n    if (initialViewState) {\n      if (initialViewState.bounds) {\n        map.fitBounds(initialViewState.bounds, {\n          ...initialViewState.fitBoundsOptions,\n          duration: 0\n        });\n      } else {\n        that._updateViewState(initialViewState, false);\n      }\n    }\n    // Simulate load event\n    if (map.isStyleLoaded()) {\n      map.fire('load');\n    } else {\n      map.once('styledata', () => map.fire('load'));\n    }\n    // Force reload\n    // @ts-ignore\n    map._update();\n    return that;\n  }\n  /* eslint-disable complexity,max-statements */\n  _initialize(container) {\n    const {\n      props\n    } = this;\n    const {\n      mapStyle = DEFAULT_STYLE\n    } = props;\n    const mapOptions = {\n      ...props,\n      ...props.initialViewState,\n      accessToken: props.mapboxAccessToken || getAccessTokenFromEnv() || null,\n      container,\n      style: normalizeStyle(mapStyle)\n    };\n    const viewState = mapOptions.initialViewState || mapOptions.viewState || mapOptions;\n    Object.assign(mapOptions, {\n      center: [viewState.longitude || 0, viewState.latitude || 0],\n      zoom: viewState.zoom || 0,\n      pitch: viewState.pitch || 0,\n      bearing: viewState.bearing || 0\n    });\n    if (props.gl) {\n      // eslint-disable-next-line\n      const getContext = HTMLCanvasElement.prototype.getContext;\n      // Hijack canvas.getContext to return our own WebGLContext\n      // This will be called inside the mapboxgl.Map constructor\n      // @ts-expect-error\n      HTMLCanvasElement.prototype.getContext = () => {\n        // Unhijack immediately\n        HTMLCanvasElement.prototype.getContext = getContext;\n        return props.gl;\n      };\n    }\n    const map = new this._MapClass(mapOptions);\n    // Props that are not part of constructor options\n    if (viewState.padding) {\n      map.setPadding(viewState.padding);\n    }\n    if (props.cursor) {\n      map.getCanvas().style.cursor = props.cursor;\n    }\n    this._createShadowTransform(map);\n    // Hack\n    // Insert code into map's render cycle\n    const renderMap = map._render;\n    map._render = arg => {\n      this._inRender = true;\n      renderMap.call(map, arg);\n      this._inRender = false;\n    };\n    const runRenderTaskQueue = map._renderTaskQueue.run;\n    map._renderTaskQueue.run = arg => {\n      runRenderTaskQueue.call(map._renderTaskQueue, arg);\n      this._onBeforeRepaint();\n    };\n    map.on('render', () => this._onAfterRepaint());\n    // Insert code into map's event pipeline\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const fireEvent = map.fire;\n    map.fire = this._fireEvent.bind(this, fireEvent);\n    // add listeners\n    map.on('resize', () => {\n      this._renderTransform.resize(map.transform.width, map.transform.height);\n    });\n    map.on('styledata', () => {\n      this._updateStyleComponents(this.props, {});\n      // Projection can be set in stylesheet\n      syncProjection(map.transform, this._renderTransform);\n    });\n    map.on('sourcedata', () => this._updateStyleComponents(this.props, {}));\n    for (const eventName in pointerEvents) {\n      map.on(eventName, this._onPointerEvent);\n    }\n    for (const eventName in cameraEvents) {\n      map.on(eventName, this._onCameraEvent);\n    }\n    for (const eventName in otherEvents) {\n      map.on(eventName, this._onEvent);\n    }\n    this._map = map;\n  }\n  /* eslint-enable complexity,max-statements */\n  recycle() {\n    // Clean up unnecessary elements before storing for reuse.\n    const container = this.map.getContainer();\n    const children = container.querySelector('[mapboxgl-children]');\n    children === null || children === void 0 ? void 0 : children.remove();\n    Mapbox.savedMaps.push(this);\n  }\n  destroy() {\n    this._map.remove();\n  }\n  // Force redraw the map now. Typically resize() and jumpTo() is reflected in the next\n  // render cycle, which is managed by Mapbox's animation loop.\n  // This removes the synchronization issue caused by requestAnimationFrame.\n  redraw() {\n    const map = this._map;\n    // map._render will throw error if style does not exist\n    // https://github.com/mapbox/mapbox-gl-js/blob/fb9fc316da14e99ff4368f3e4faa3888fb43c513\n    //   /src/ui/map.js#L1834\n    if (!this._inRender && map.style) {\n      // cancel the scheduled update\n      if (map._frame) {\n        map._frame.cancel();\n        map._frame = null;\n      }\n      // the order is important - render() may schedule another update\n      map._render();\n    }\n  }\n  _createShadowTransform(map) {\n    const renderTransform = cloneTransform(map.transform);\n    map.painter.transform = renderTransform;\n    this._renderTransform = renderTransform;\n  }\n  /* Trigger map resize if size is controlled\n     @param {object} nextProps\n     @returns {bool} true if size has changed\n   */\n  _updateSize(nextProps) {\n    // Check if size is controlled\n    const {\n      viewState\n    } = nextProps;\n    if (viewState) {\n      const map = this._map;\n      if (viewState.width !== map.transform.width || viewState.height !== map.transform.height) {\n        map.resize();\n        return true;\n      }\n    }\n    return false;\n  }\n  // Adapted from map.jumpTo\n  /* Update camera to match props\n     @param {object} nextProps\n     @param {bool} triggerEvents - should fire camera events\n     @returns {bool} true if anything is changed\n   */\n  _updateViewState(nextProps, triggerEvents) {\n    if (this._internalUpdate) {\n      return false;\n    }\n    const map = this._map;\n    const tr = this._renderTransform;\n    // Take a snapshot of the transform before mutation\n    const {\n      zoom,\n      pitch,\n      bearing\n    } = tr;\n    const isMoving = map.isMoving();\n    if (isMoving) {\n      // All movement of the camera is done relative to the sea level\n      tr.cameraElevationReference = 'sea';\n    }\n    const changed = applyViewStateToTransform(tr, {\n      ...transformToViewState(map.transform),\n      ...nextProps\n    });\n    if (isMoving) {\n      // Reset camera reference\n      tr.cameraElevationReference = 'ground';\n    }\n    if (changed && triggerEvents) {\n      const deferredEvents = this._deferredEvents;\n      // Delay DOM control updates to the next render cycle\n      deferredEvents.move = true;\n      deferredEvents.zoom || (deferredEvents.zoom = zoom !== tr.zoom);\n      deferredEvents.rotate || (deferredEvents.rotate = bearing !== tr.bearing);\n      deferredEvents.pitch || (deferredEvents.pitch = pitch !== tr.pitch);\n    }\n    // Avoid manipulating the real transform when interaction/animation is ongoing\n    // as it would interfere with Mapbox's handlers\n    if (!isMoving) {\n      applyViewStateToTransform(map.transform, nextProps);\n    }\n    return changed;\n  }\n  /* Update camera constraints and projection settings to match props\n     @param {object} nextProps\n     @param {object} currProps\n     @returns {bool} true if anything is changed\n   */\n  _updateSettings(nextProps, currProps) {\n    const map = this._map;\n    let changed = false;\n    for (const propName of settingNames) {\n      if (propName in nextProps && !deepEqual(nextProps[propName], currProps[propName])) {\n        changed = true;\n        const setter = map[`set${propName[0].toUpperCase()}${propName.slice(1)}`];\n        setter === null || setter === void 0 ? void 0 : setter.call(map, nextProps[propName]);\n      }\n    }\n    return changed;\n  }\n  /* Update map style to match props\n     @param {object} nextProps\n     @param {object} currProps\n     @returns {bool} true if style is changed\n   */\n  _updateStyle(nextProps, currProps) {\n    if (nextProps.cursor !== currProps.cursor) {\n      this._map.getCanvas().style.cursor = nextProps.cursor || '';\n    }\n    if (nextProps.mapStyle !== currProps.mapStyle) {\n      const {\n        mapStyle = DEFAULT_STYLE,\n        styleDiffing = true\n      } = nextProps;\n      const options = {\n        diff: styleDiffing\n      };\n      if ('localIdeographFontFamily' in nextProps) {\n        // @ts-ignore Mapbox specific prop\n        options.localIdeographFontFamily = nextProps.localIdeographFontFamily;\n      }\n      this._map.setStyle(normalizeStyle(mapStyle), options);\n      return true;\n    }\n    return false;\n  }\n  /* Update fog, light and terrain to match props\n     @param {object} nextProps\n     @param {object} currProps\n     @returns {bool} true if anything is changed\n   */\n  _updateStyleComponents(nextProps, currProps) {\n    const map = this._map;\n    let changed = false;\n    if (map.isStyleLoaded()) {\n      if ('light' in nextProps && map.setLight && !deepEqual(nextProps.light, currProps.light)) {\n        changed = true;\n        map.setLight(nextProps.light);\n      }\n      if ('fog' in nextProps && map.setFog && !deepEqual(nextProps.fog, currProps.fog)) {\n        changed = true;\n        map.setFog(nextProps.fog);\n      }\n      if ('terrain' in nextProps && map.setTerrain && !deepEqual(nextProps.terrain, currProps.terrain)) {\n        if (!nextProps.terrain || map.getSource(nextProps.terrain.source)) {\n          changed = true;\n          map.setTerrain(nextProps.terrain);\n        }\n      }\n    }\n    return changed;\n  }\n  /* Update interaction handlers to match props\n     @param {object} nextProps\n     @param {object} currProps\n     @returns {bool} true if anything is changed\n   */\n  _updateHandlers(nextProps, currProps) {\n    var _a, _b;\n    const map = this._map;\n    let changed = false;\n    for (const propName of handlerNames) {\n      const newValue = (_a = nextProps[propName]) !== null && _a !== void 0 ? _a : true;\n      const oldValue = (_b = currProps[propName]) !== null && _b !== void 0 ? _b : true;\n      if (!deepEqual(newValue, oldValue)) {\n        changed = true;\n        if (newValue) {\n          map[propName].enable(newValue);\n        } else {\n          map[propName].disable();\n        }\n      }\n    }\n    return changed;\n  }\n  _queryRenderedFeatures(point) {\n    const map = this._map;\n    const tr = map.transform;\n    const {\n      interactiveLayerIds = []\n    } = this.props;\n    try {\n      map.transform = this._renderTransform;\n      return map.queryRenderedFeatures(point, {\n        layers: interactiveLayerIds.filter(map.getLayer.bind(map))\n      });\n    } catch (_a) {\n      // May fail if style is not loaded\n      return [];\n    } finally {\n      map.transform = tr;\n    }\n  }\n  _updateHover(e) {\n    var _a;\n    const {\n      props\n    } = this;\n    const shouldTrackHoveredFeatures = props.interactiveLayerIds && (props.onMouseMove || props.onMouseEnter || props.onMouseLeave);\n    if (shouldTrackHoveredFeatures) {\n      const eventType = e.type;\n      const wasHovering = ((_a = this._hoveredFeatures) === null || _a === void 0 ? void 0 : _a.length) > 0;\n      const features = this._queryRenderedFeatures(e.point);\n      const isHovering = features.length > 0;\n      if (!isHovering && wasHovering) {\n        e.type = 'mouseleave';\n        this._onPointerEvent(e);\n      }\n      this._hoveredFeatures = features;\n      if (isHovering && !wasHovering) {\n        e.type = 'mouseenter';\n        this._onPointerEvent(e);\n      }\n      e.type = eventType;\n    } else {\n      this._hoveredFeatures = null;\n    }\n  }\n  _fireEvent(baseFire, event, properties) {\n    const map = this._map;\n    const tr = map.transform;\n    const eventType = typeof event === 'string' ? event : event.type;\n    if (eventType === 'move') {\n      this._updateViewState(this.props, false);\n    }\n    if (eventType in cameraEvents) {\n      if (typeof event === 'object') {\n        event.viewState = transformToViewState(tr);\n      }\n      if (this._map.isMoving()) {\n        // Replace map.transform with ours during the callbacks\n        map.transform = this._renderTransform;\n        baseFire.call(map, event, properties);\n        map.transform = tr;\n        return map;\n      }\n    }\n    baseFire.call(map, event, properties);\n    return map;\n  }\n  // All camera manipulations are complete, ready to repaint\n  _onBeforeRepaint() {\n    const map = this._map;\n    // If there are camera changes driven by props, invoke camera events so that DOM controls are synced\n    this._internalUpdate = true;\n    for (const eventType in this._deferredEvents) {\n      if (this._deferredEvents[eventType]) {\n        map.fire(eventType);\n      }\n    }\n    this._internalUpdate = false;\n    const tr = this._map.transform;\n    // Make sure camera matches the current props\n    map.transform = this._renderTransform;\n    this._onAfterRepaint = () => {\n      // Mapbox transitions between non-mercator projection and mercator during render time\n      // Copy it back to the other\n      syncProjection(this._renderTransform, tr);\n      // Restores camera state before render/load events are fired\n      map.transform = tr;\n    };\n  }\n}\nMapbox.savedMaps = [];\n/**\n * Access token can be provided via one of:\n *   mapboxAccessToken prop\n *   access_token query parameter\n *   MapboxAccessToken environment variable\n *   REACT_APP_MAPBOX_ACCESS_TOKEN environment variable\n * @returns access token\n */\nfunction getAccessTokenFromEnv() {\n  let accessToken = null;\n  /* global location, process */\n  if (typeof location !== 'undefined') {\n    const match = /access_token=([^&\\/]*)/.exec(location.search);\n    accessToken = match && match[1];\n  }\n  // Note: This depends on bundler plugins (e.g. webpack) importing environment correctly\n  try {\n    accessToken = accessToken || process.env.MapboxAccessToken;\n  } catch (_a) {\n    // ignore\n  }\n  try {\n    accessToken = accessToken || process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;\n  } catch (_b) {\n    // ignore\n  }\n  return accessToken;\n}", "map": {"version": 3, "names": ["transformToViewState", "applyViewStateToTransform", "cloneTransform", "syncProjection", "normalizeStyle", "deepEqual", "DEFAULT_STYLE", "version", "sources", "layers", "pointerEvents", "mousedown", "mouseup", "mouseover", "mousemove", "click", "dblclick", "mouseenter", "mouseleave", "mouseout", "contextmenu", "touchstart", "touchend", "touchmove", "touchcancel", "cameraEvents", "movestart", "move", "moveend", "dragstart", "drag", "dragend", "zoomstart", "zoom", "zoomend", "rotatestart", "rotate", "rotateend", "pitchstart", "pitch", "pitchend", "otherEvents", "wheel", "boxzoomstart", "boxzoomend", "boxzoomcancel", "resize", "load", "render", "idle", "remove", "data", "styledata", "sourcedata", "error", "settingNames", "handlerNames", "Mapbox", "constructor", "MapClass", "props", "container", "_map", "_internalUpdate", "_inRender", "_hoveredFeatures", "_deferredEvents", "_onEvent", "e", "cb", "type", "console", "_onPointerEvent", "_updateHover", "interactiveLayerIds", "features", "_queryRenderedFeatures", "point", "_onCameraEvent", "_MapClass", "_initialize", "map", "transform", "_renderTransform", "setProps", "oldProps", "settingsChanged", "_updateSettings", "_createShadowTransform", "sizeChanged", "_updateSize", "viewStateChanged", "_updateViewState", "_updateStyle", "_updateStyleComponents", "_updateHandlers", "isMoving", "redraw", "reuse", "that", "savedMaps", "pop", "oldContainer", "getContainer", "className", "childNodes", "length", "append<PERSON><PERSON><PERSON>", "_container", "resizeObserver", "_resizeObserver", "disconnect", "observe", "styleDiffing", "initialViewState", "bounds", "fitBounds", "fitBoundsOptions", "duration", "isStyleLoaded", "fire", "once", "_update", "mapStyle", "mapOptions", "accessToken", "mapboxAccessToken", "getAccessTokenFromEnv", "style", "viewState", "Object", "assign", "center", "longitude", "latitude", "bearing", "gl", "getContext", "HTMLCanvasElement", "prototype", "padding", "setPadding", "cursor", "get<PERSON>anvas", "renderMap", "_render", "arg", "call", "runRenderTaskQueue", "_renderTaskQueue", "run", "_onBeforeRepaint", "on", "_onAfterRepaint", "fireEvent", "_fireEvent", "bind", "width", "height", "eventName", "recycle", "children", "querySelector", "push", "destroy", "_frame", "cancel", "renderTransform", "painter", "nextProps", "triggerEvents", "tr", "cameraElevationReference", "changed", "deferredEvents", "currProps", "propName", "setter", "toUpperCase", "slice", "options", "diff", "localIdeographFontFamily", "setStyle", "setLight", "light", "setFog", "fog", "setTerrain", "terrain", "getSource", "source", "newValue", "_a", "oldValue", "_b", "enable", "disable", "queryRenderedFeatures", "filter", "<PERSON><PERSON><PERSON><PERSON>", "shouldTrackHoveredFeatures", "onMouseMove", "onMouseEnter", "onMouseLeave", "eventType", "wasHovering", "isHovering", "baseFire", "event", "properties", "location", "match", "exec", "search", "process", "env", "MapboxAccessToken", "REACT_APP_MAPBOX_ACCESS_TOKEN"], "sources": ["C:\\Users\\<USER>\\Downloads\\morocco-complete-map-geojson-topojson-main\\morocco-vector-tiles\\node_modules\\react-map-gl\\src\\mapbox\\mapbox.ts"], "sourcesContent": ["import {\n  transformToViewState,\n  applyViewStateToTransform,\n  cloneTransform,\n  syncProjection\n} from '../utils/transform';\nimport {normalizeStyle} from '../utils/style-utils';\nimport {deepEqual} from '../utils/deep-equal';\n\nimport type {\n  Transform,\n  ViewState,\n  ViewStateChangeEvent,\n  Point,\n  PointLike,\n  PaddingOptions,\n  MapStyle,\n  ImmutableLike,\n  LngLatBoundsLike,\n  Callbacks,\n  MapEvent,\n  ErrorEvent,\n  MapMouseEvent,\n  MapGeoJSONFeature,\n  MapInstance,\n  MapInstanceInternal\n} from '../types';\n\nexport type MapboxProps<\n  StyleT extends MapStyle = MapStyle,\n  CallbacksT extends Callbacks = {}\n> = Partial<ViewState> &\n  CallbacksT & {\n    // Init options\n    mapboxAccessToken?: string;\n\n    /** Camera options used when constructing the Map instance */\n    initialViewState?: Partial<ViewState> & {\n      /** The initial bounds of the map. If bounds is specified, it overrides longitude, latitude and zoom options. */\n      bounds?: LngLatBoundsLike;\n      /** A fitBounds options object to use only when setting the bounds option. */\n      fitBoundsOptions?: {\n        offset?: PointLike;\n        minZoom?: number;\n        maxZoom?: number;\n        padding?: number | PaddingOptions;\n      };\n    };\n\n    /** If provided, render into an external WebGL context */\n    gl?: WebGLRenderingContext;\n\n    /** For external controller to override the camera state */\n    viewState?: ViewState & {\n      width: number;\n      height: number;\n    };\n\n    // Styling\n\n    /** Mapbox style */\n    mapStyle?: string | StyleT | ImmutableLike<StyleT>;\n    /** Enable diffing when the map style changes\n     * @default true\n     */\n    styleDiffing?: boolean;\n    /** The fog property of the style. Must conform to the Fog Style Specification .\n     * If `undefined` is provided, removes the fog from the map. */\n    fog?: StyleT['fog'];\n    /** Light properties of the map. */\n    light?: StyleT['light'];\n    /** Terrain property of the style. Must conform to the Terrain Style Specification .\n     * If `undefined` is provided, removes terrain from the map. */\n    terrain?: StyleT['terrain'];\n\n    /** Default layers to query on pointer events */\n    interactiveLayerIds?: string[];\n    /** CSS cursor */\n    cursor?: string;\n  };\n\nconst DEFAULT_STYLE = {version: 8, sources: {}, layers: []} as MapStyle;\n\nconst pointerEvents = {\n  mousedown: 'onMouseDown',\n  mouseup: 'onMouseUp',\n  mouseover: 'onMouseOver',\n  mousemove: 'onMouseMove',\n  click: 'onClick',\n  dblclick: 'onDblClick',\n  mouseenter: 'onMouseEnter',\n  mouseleave: 'onMouseLeave',\n  mouseout: 'onMouseOut',\n  contextmenu: 'onContextMenu',\n  touchstart: 'onTouchStart',\n  touchend: 'onTouchEnd',\n  touchmove: 'onTouchMove',\n  touchcancel: 'onTouchCancel'\n};\nconst cameraEvents = {\n  movestart: 'onMoveStart',\n  move: 'onMove',\n  moveend: 'onMoveEnd',\n  dragstart: 'onDragStart',\n  drag: 'onDrag',\n  dragend: 'onDragEnd',\n  zoomstart: 'onZoomStart',\n  zoom: 'onZoom',\n  zoomend: 'onZoomEnd',\n  rotatestart: 'onRotateStart',\n  rotate: 'onRotate',\n  rotateend: 'onRotateEnd',\n  pitchstart: 'onPitchStart',\n  pitch: 'onPitch',\n  pitchend: 'onPitchEnd'\n};\nconst otherEvents = {\n  wheel: 'onWheel',\n  boxzoomstart: 'onBoxZoomStart',\n  boxzoomend: 'onBoxZoomEnd',\n  boxzoomcancel: 'onBoxZoomCancel',\n  resize: 'onResize',\n  load: 'onLoad',\n  render: 'onRender',\n  idle: 'onIdle',\n  remove: 'onRemove',\n  data: 'onData',\n  styledata: 'onStyleData',\n  sourcedata: 'onSourceData',\n  error: 'onError'\n};\nconst settingNames = [\n  'minZoom',\n  'maxZoom',\n  'minPitch',\n  'maxPitch',\n  'maxBounds',\n  'projection',\n  'renderWorldCopies'\n];\nconst handlerNames = [\n  'scrollZoom',\n  'boxZoom',\n  'dragRotate',\n  'dragPan',\n  'keyboard',\n  'doubleClickZoom',\n  'touchZoomRotate',\n  'touchPitch'\n];\n\n/**\n * A wrapper for mapbox-gl's Map class\n */\nexport default class Mapbox<\n  StyleT extends MapStyle = MapStyle,\n  CallbacksT extends Callbacks = {},\n  MapT extends MapInstance = MapInstance\n> {\n  private _MapClass: {new (options: any): MapInstance};\n  // mapboxgl.Map instance\n  private _map: MapInstanceInternal<MapT> = null;\n  // User-supplied props\n  props: MapboxProps<StyleT, CallbacksT>;\n\n  // Mapbox map is stateful.\n  // During method calls/user interactions, map.transform is mutated and\n  // deviate from user-supplied props.\n  // In order to control the map reactively, we shadow the transform\n  // with the one below, which reflects the view state resolved from\n  // both user-supplied props and the underlying state\n  private _renderTransform: Transform;\n\n  // Internal states\n  private _internalUpdate: boolean = false;\n  private _inRender: boolean = false;\n  private _hoveredFeatures: MapGeoJSONFeature[] = null;\n  private _deferredEvents: {\n    move: boolean;\n    zoom: boolean;\n    pitch: boolean;\n    rotate: boolean;\n  } = {\n    move: false,\n    zoom: false,\n    pitch: false,\n    rotate: false\n  };\n\n  static savedMaps: Mapbox[] = [];\n\n  constructor(\n    MapClass: {new (options: any): MapInstance},\n    props: MapboxProps<StyleT, CallbacksT>,\n    container: HTMLDivElement\n  ) {\n    this._MapClass = MapClass;\n    this.props = props;\n    this._initialize(container);\n  }\n\n  get map(): MapT {\n    return this._map;\n  }\n\n  get transform(): Transform {\n    return this._renderTransform;\n  }\n\n  setProps(props: MapboxProps<StyleT, CallbacksT>) {\n    const oldProps = this.props;\n    this.props = props;\n\n    const settingsChanged = this._updateSettings(props, oldProps);\n    if (settingsChanged) {\n      this._createShadowTransform(this._map);\n    }\n    const sizeChanged = this._updateSize(props);\n    const viewStateChanged = this._updateViewState(props, true);\n    this._updateStyle(props, oldProps);\n    this._updateStyleComponents(props, oldProps);\n    this._updateHandlers(props, oldProps);\n\n    // If 1) view state has changed to match props and\n    //    2) the props change is not triggered by map events,\n    // it's driven by an external state change. Redraw immediately\n    if (settingsChanged || sizeChanged || (viewStateChanged && !this._map.isMoving())) {\n      this.redraw();\n    }\n  }\n\n  static reuse<StyleT extends MapStyle, CallbacksT extends Callbacks, MapT extends MapInstance>(\n    props: MapboxProps<StyleT, CallbacksT>,\n    container: HTMLDivElement\n  ): Mapbox<StyleT, CallbacksT, MapT> {\n    const that = Mapbox.savedMaps.pop() as Mapbox<StyleT, CallbacksT, MapT>;\n    if (!that) {\n      return null;\n    }\n\n    const map = that.map;\n    // When reusing the saved map, we need to reparent the map(canvas) and other child nodes\n    // intoto the new container from the props.\n    // Step 1: reparenting child nodes from old container to new container\n    const oldContainer = map.getContainer();\n    container.className = oldContainer.className;\n    while (oldContainer.childNodes.length > 0) {\n      container.appendChild(oldContainer.childNodes[0]);\n    }\n    // Step 2: replace the internal container with new container from the react component\n    // @ts-ignore\n    map._container = container;\n\n    // With maplibre-gl as mapLib, map uses ResizeObserver to observe when its container resizes.\n    // When reusing the saved map, we need to disconnect the observer and observe the new container.\n    // Step 3: telling the ResizeObserver to disconnect and observe the new container\n    // @ts-ignore\n    const resizeObserver = map._resizeObserver;\n    if (resizeObserver) {\n      resizeObserver.disconnect();\n      resizeObserver.observe(container);\n    }\n\n    // Step 4: apply new props\n    that.setProps({...props, styleDiffing: false});\n    map.resize();\n    const {initialViewState} = props;\n    if (initialViewState) {\n      if (initialViewState.bounds) {\n        map.fitBounds(initialViewState.bounds, {...initialViewState.fitBoundsOptions, duration: 0});\n      } else {\n        that._updateViewState(initialViewState, false);\n      }\n    }\n\n    // Simulate load event\n    if (map.isStyleLoaded()) {\n      map.fire('load');\n    } else {\n      map.once('styledata', () => map.fire('load'));\n    }\n\n    // Force reload\n    // @ts-ignore\n    map._update();\n    return that;\n  }\n\n  /* eslint-disable complexity,max-statements */\n  _initialize(container: HTMLDivElement) {\n    const {props} = this;\n    const {mapStyle = DEFAULT_STYLE} = props;\n    const mapOptions = {\n      ...props,\n      ...props.initialViewState,\n      accessToken: props.mapboxAccessToken || getAccessTokenFromEnv() || null,\n      container,\n      style: normalizeStyle(mapStyle)\n    };\n\n    const viewState = mapOptions.initialViewState || mapOptions.viewState || mapOptions;\n    Object.assign(mapOptions, {\n      center: [viewState.longitude || 0, viewState.latitude || 0],\n      zoom: viewState.zoom || 0,\n      pitch: viewState.pitch || 0,\n      bearing: viewState.bearing || 0\n    });\n\n    if (props.gl) {\n      // eslint-disable-next-line\n      const getContext = HTMLCanvasElement.prototype.getContext;\n      // Hijack canvas.getContext to return our own WebGLContext\n      // This will be called inside the mapboxgl.Map constructor\n      // @ts-expect-error\n      HTMLCanvasElement.prototype.getContext = () => {\n        // Unhijack immediately\n        HTMLCanvasElement.prototype.getContext = getContext;\n        return props.gl;\n      };\n    }\n\n    const map = new this._MapClass(mapOptions) as MapInstanceInternal<MapT>;\n    // Props that are not part of constructor options\n    if (viewState.padding) {\n      map.setPadding(viewState.padding);\n    }\n    if (props.cursor) {\n      map.getCanvas().style.cursor = props.cursor;\n    }\n    this._createShadowTransform(map);\n\n    // Hack\n    // Insert code into map's render cycle\n    const renderMap = map._render;\n    map._render = (arg: number) => {\n      this._inRender = true;\n      renderMap.call(map, arg);\n      this._inRender = false;\n    };\n    const runRenderTaskQueue = map._renderTaskQueue.run;\n    map._renderTaskQueue.run = (arg: number) => {\n      runRenderTaskQueue.call(map._renderTaskQueue, arg);\n      this._onBeforeRepaint();\n    };\n    map.on('render', () => this._onAfterRepaint());\n    // Insert code into map's event pipeline\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const fireEvent = map.fire;\n    map.fire = this._fireEvent.bind(this, fireEvent);\n\n    // add listeners\n    map.on('resize', () => {\n      this._renderTransform.resize(map.transform.width, map.transform.height);\n    });\n    map.on('styledata', () => {\n      this._updateStyleComponents(this.props, {});\n      // Projection can be set in stylesheet\n      syncProjection(map.transform, this._renderTransform);\n    });\n    map.on('sourcedata', () => this._updateStyleComponents(this.props, {}));\n    for (const eventName in pointerEvents) {\n      map.on(eventName, this._onPointerEvent);\n    }\n    for (const eventName in cameraEvents) {\n      map.on(eventName, this._onCameraEvent);\n    }\n    for (const eventName in otherEvents) {\n      map.on(eventName, this._onEvent);\n    }\n    this._map = map;\n  }\n  /* eslint-enable complexity,max-statements */\n\n  recycle() {\n    // Clean up unnecessary elements before storing for reuse.\n    const container = this.map.getContainer();\n    const children = container.querySelector('[mapboxgl-children]');\n    children?.remove();\n\n    Mapbox.savedMaps.push(this);\n  }\n\n  destroy() {\n    this._map.remove();\n  }\n\n  // Force redraw the map now. Typically resize() and jumpTo() is reflected in the next\n  // render cycle, which is managed by Mapbox's animation loop.\n  // This removes the synchronization issue caused by requestAnimationFrame.\n  redraw() {\n    const map = this._map as any;\n    // map._render will throw error if style does not exist\n    // https://github.com/mapbox/mapbox-gl-js/blob/fb9fc316da14e99ff4368f3e4faa3888fb43c513\n    //   /src/ui/map.js#L1834\n    if (!this._inRender && map.style) {\n      // cancel the scheduled update\n      if (map._frame) {\n        map._frame.cancel();\n        map._frame = null;\n      }\n      // the order is important - render() may schedule another update\n      map._render();\n    }\n  }\n\n  _createShadowTransform(map: any) {\n    const renderTransform = cloneTransform(map.transform);\n    map.painter.transform = renderTransform;\n\n    this._renderTransform = renderTransform;\n  }\n\n  /* Trigger map resize if size is controlled\n     @param {object} nextProps\n     @returns {bool} true if size has changed\n   */\n  _updateSize(nextProps: MapboxProps<StyleT>): boolean {\n    // Check if size is controlled\n    const {viewState} = nextProps;\n    if (viewState) {\n      const map = this._map;\n      if (viewState.width !== map.transform.width || viewState.height !== map.transform.height) {\n        map.resize();\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // Adapted from map.jumpTo\n  /* Update camera to match props\n     @param {object} nextProps\n     @param {bool} triggerEvents - should fire camera events\n     @returns {bool} true if anything is changed\n   */\n  _updateViewState(nextProps: MapboxProps<StyleT>, triggerEvents: boolean): boolean {\n    if (this._internalUpdate) {\n      return false;\n    }\n    const map = this._map;\n\n    const tr = this._renderTransform;\n    // Take a snapshot of the transform before mutation\n    const {zoom, pitch, bearing} = tr;\n    const isMoving = map.isMoving();\n\n    if (isMoving) {\n      // All movement of the camera is done relative to the sea level\n      tr.cameraElevationReference = 'sea';\n    }\n    const changed = applyViewStateToTransform(tr, {\n      ...transformToViewState(map.transform),\n      ...nextProps\n    });\n    if (isMoving) {\n      // Reset camera reference\n      tr.cameraElevationReference = 'ground';\n    }\n\n    if (changed && triggerEvents) {\n      const deferredEvents = this._deferredEvents;\n      // Delay DOM control updates to the next render cycle\n      deferredEvents.move = true;\n      deferredEvents.zoom ||= zoom !== tr.zoom;\n      deferredEvents.rotate ||= bearing !== tr.bearing;\n      deferredEvents.pitch ||= pitch !== tr.pitch;\n    }\n\n    // Avoid manipulating the real transform when interaction/animation is ongoing\n    // as it would interfere with Mapbox's handlers\n    if (!isMoving) {\n      applyViewStateToTransform(map.transform, nextProps);\n    }\n\n    return changed;\n  }\n\n  /* Update camera constraints and projection settings to match props\n     @param {object} nextProps\n     @param {object} currProps\n     @returns {bool} true if anything is changed\n   */\n  _updateSettings(nextProps: MapboxProps<StyleT>, currProps: MapboxProps<StyleT>): boolean {\n    const map = this._map;\n    let changed = false;\n    for (const propName of settingNames) {\n      if (propName in nextProps && !deepEqual(nextProps[propName], currProps[propName])) {\n        changed = true;\n        const setter = map[`set${propName[0].toUpperCase()}${propName.slice(1)}`];\n        setter?.call(map, nextProps[propName]);\n      }\n    }\n    return changed;\n  }\n\n  /* Update map style to match props\n     @param {object} nextProps\n     @param {object} currProps\n     @returns {bool} true if style is changed\n   */\n  _updateStyle(nextProps: MapboxProps<StyleT>, currProps: MapboxProps<StyleT>): boolean {\n    if (nextProps.cursor !== currProps.cursor) {\n      this._map.getCanvas().style.cursor = nextProps.cursor || '';\n    }\n    if (nextProps.mapStyle !== currProps.mapStyle) {\n      const {mapStyle = DEFAULT_STYLE, styleDiffing = true} = nextProps;\n      const options: any = {\n        diff: styleDiffing\n      };\n      if ('localIdeographFontFamily' in nextProps) {\n        // @ts-ignore Mapbox specific prop\n        options.localIdeographFontFamily = nextProps.localIdeographFontFamily;\n      }\n      this._map.setStyle(normalizeStyle(mapStyle), options);\n      return true;\n    }\n    return false;\n  }\n\n  /* Update fog, light and terrain to match props\n     @param {object} nextProps\n     @param {object} currProps\n     @returns {bool} true if anything is changed\n   */\n  _updateStyleComponents(nextProps: MapboxProps<StyleT>, currProps: MapboxProps<StyleT>): boolean {\n    const map = this._map;\n    let changed = false;\n    if (map.isStyleLoaded()) {\n      if ('light' in nextProps && map.setLight && !deepEqual(nextProps.light, currProps.light)) {\n        changed = true;\n        map.setLight(nextProps.light);\n      }\n      if ('fog' in nextProps && map.setFog && !deepEqual(nextProps.fog, currProps.fog)) {\n        changed = true;\n        map.setFog(nextProps.fog);\n      }\n      if (\n        'terrain' in nextProps &&\n        map.setTerrain &&\n        !deepEqual(nextProps.terrain, currProps.terrain)\n      ) {\n        if (!nextProps.terrain || map.getSource(nextProps.terrain.source)) {\n          changed = true;\n          map.setTerrain(nextProps.terrain);\n        }\n      }\n    }\n    return changed;\n  }\n\n  /* Update interaction handlers to match props\n     @param {object} nextProps\n     @param {object} currProps\n     @returns {bool} true if anything is changed\n   */\n  _updateHandlers(nextProps: MapboxProps<StyleT>, currProps: MapboxProps<StyleT>): boolean {\n    const map = this._map;\n    let changed = false;\n    for (const propName of handlerNames) {\n      const newValue = nextProps[propName] ?? true;\n      const oldValue = currProps[propName] ?? true;\n      if (!deepEqual(newValue, oldValue)) {\n        changed = true;\n        if (newValue) {\n          map[propName].enable(newValue);\n        } else {\n          map[propName].disable();\n        }\n      }\n    }\n    return changed;\n  }\n\n  _onEvent = (e: MapEvent<MapT>) => {\n    // @ts-ignore\n    const cb = this.props[otherEvents[e.type]];\n    if (cb) {\n      cb(e);\n    } else if (e.type === 'error') {\n      console.error((e as ErrorEvent<MapT>).error); // eslint-disable-line\n    }\n  };\n\n  private _queryRenderedFeatures(point: Point) {\n    const map = this._map;\n    const tr = map.transform;\n    const {interactiveLayerIds = []} = this.props;\n    try {\n      map.transform = this._renderTransform;\n      return map.queryRenderedFeatures(point, {\n        layers: interactiveLayerIds.filter(map.getLayer.bind(map))\n      });\n    } catch {\n      // May fail if style is not loaded\n      return [];\n    } finally {\n      map.transform = tr;\n    }\n  }\n\n  _updateHover(e: MapMouseEvent<MapT>) {\n    const {props} = this;\n    const shouldTrackHoveredFeatures =\n      props.interactiveLayerIds && (props.onMouseMove || props.onMouseEnter || props.onMouseLeave);\n\n    if (shouldTrackHoveredFeatures) {\n      const eventType = e.type;\n      const wasHovering = this._hoveredFeatures?.length > 0;\n      const features = this._queryRenderedFeatures(e.point);\n      const isHovering = features.length > 0;\n\n      if (!isHovering && wasHovering) {\n        e.type = 'mouseleave';\n        this._onPointerEvent(e);\n      }\n      this._hoveredFeatures = features;\n      if (isHovering && !wasHovering) {\n        e.type = 'mouseenter';\n        this._onPointerEvent(e);\n      }\n      e.type = eventType;\n    } else {\n      this._hoveredFeatures = null;\n    }\n  }\n\n  _onPointerEvent = (e: MapMouseEvent<MapT> | MapMouseEvent<MapT>) => {\n    if (e.type === 'mousemove' || e.type === 'mouseout') {\n      this._updateHover(e);\n    }\n\n    // @ts-ignore\n    const cb = this.props[pointerEvents[e.type]];\n    if (cb) {\n      if (this.props.interactiveLayerIds && e.type !== 'mouseover' && e.type !== 'mouseout') {\n        e.features = this._hoveredFeatures || this._queryRenderedFeatures(e.point);\n      }\n      cb(e);\n      delete e.features;\n    }\n  };\n\n  _onCameraEvent = (e: ViewStateChangeEvent<MapT>) => {\n    if (!this._internalUpdate) {\n      // @ts-ignore\n      const cb = this.props[cameraEvents[e.type]];\n      if (cb) {\n        cb(e);\n      }\n    }\n    if (e.type in this._deferredEvents) {\n      this._deferredEvents[e.type] = false;\n    }\n  };\n\n  _fireEvent(baseFire: Function, event: string | MapEvent<MapT>, properties?: object) {\n    const map = this._map;\n    const tr = map.transform;\n\n    const eventType = typeof event === 'string' ? event : event.type;\n    if (eventType === 'move') {\n      this._updateViewState(this.props, false);\n    }\n    if (eventType in cameraEvents) {\n      if (typeof event === 'object') {\n        (event as unknown as ViewStateChangeEvent<MapT>).viewState = transformToViewState(tr);\n      }\n      if (this._map.isMoving()) {\n        // Replace map.transform with ours during the callbacks\n        map.transform = this._renderTransform;\n        baseFire.call(map, event, properties);\n        map.transform = tr;\n\n        return map;\n      }\n    }\n    baseFire.call(map, event, properties);\n\n    return map;\n  }\n\n  // All camera manipulations are complete, ready to repaint\n  _onBeforeRepaint() {\n    const map = this._map;\n\n    // If there are camera changes driven by props, invoke camera events so that DOM controls are synced\n    this._internalUpdate = true;\n    for (const eventType in this._deferredEvents) {\n      if (this._deferredEvents[eventType]) {\n        map.fire(eventType);\n      }\n    }\n    this._internalUpdate = false;\n\n    const tr = this._map.transform;\n    // Make sure camera matches the current props\n    map.transform = this._renderTransform;\n\n    this._onAfterRepaint = () => {\n      // Mapbox transitions between non-mercator projection and mercator during render time\n      // Copy it back to the other\n      syncProjection(this._renderTransform, tr);\n      // Restores camera state before render/load events are fired\n      map.transform = tr;\n    };\n  }\n\n  _onAfterRepaint: () => void;\n}\n\n/**\n * Access token can be provided via one of:\n *   mapboxAccessToken prop\n *   access_token query parameter\n *   MapboxAccessToken environment variable\n *   REACT_APP_MAPBOX_ACCESS_TOKEN environment variable\n * @returns access token\n */\nfunction getAccessTokenFromEnv(): string {\n  let accessToken = null;\n\n  /* global location, process */\n  if (typeof location !== 'undefined') {\n    const match = /access_token=([^&\\/]*)/.exec(location.search);\n    accessToken = match && match[1];\n  }\n\n  // Note: This depends on bundler plugins (e.g. webpack) importing environment correctly\n  try {\n    accessToken = accessToken || process.env.MapboxAccessToken;\n  } catch {\n    // ignore\n  }\n\n  try {\n    accessToken = accessToken || process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;\n  } catch {\n    // ignore\n  }\n\n  return accessToken;\n}\n"], "mappings": "AAAA,SACEA,oBAAoB,EACpBC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,QACT,oBAAoB;AAC3B,SAAQC,cAAc,QAAO,sBAAsB;AACnD,SAAQC,SAAS,QAAO,qBAAqB;AA0E7C,MAAMC,aAAa,GAAG;EAACC,OAAO,EAAE,CAAC;EAAEC,OAAO,EAAE,EAAE;EAAEC,MAAM,EAAE;AAAE,CAAa;AAEvE,MAAMC,aAAa,GAAG;EACpBC,SAAS,EAAE,aAAa;EACxBC,OAAO,EAAE,WAAW;EACpBC,SAAS,EAAE,aAAa;EACxBC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,SAAS;EAChBC,QAAQ,EAAE,YAAY;EACtBC,UAAU,EAAE,cAAc;EAC1BC,UAAU,EAAE,cAAc;EAC1BC,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,eAAe;EAC5BC,UAAU,EAAE,cAAc;EAC1BC,QAAQ,EAAE,YAAY;EACtBC,SAAS,EAAE,aAAa;EACxBC,WAAW,EAAE;CACd;AACD,MAAMC,YAAY,GAAG;EACnBC,SAAS,EAAE,aAAa;EACxBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,WAAW;EACpBC,SAAS,EAAE,aAAa;EACxBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,WAAW;EACpBC,SAAS,EAAE,aAAa;EACxBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,WAAW;EACpBC,WAAW,EAAE,eAAe;EAC5BC,MAAM,EAAE,UAAU;EAClBC,SAAS,EAAE,aAAa;EACxBC,UAAU,EAAE,cAAc;EAC1BC,KAAK,EAAE,SAAS;EAChBC,QAAQ,EAAE;CACX;AACD,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,SAAS;EAChBC,YAAY,EAAE,gBAAgB;EAC9BC,UAAU,EAAE,cAAc;EAC1BC,aAAa,EAAE,iBAAiB;EAChCC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,aAAa;EACxBC,UAAU,EAAE,cAAc;EAC1BC,KAAK,EAAE;CACR;AACD,MAAMC,YAAY,GAAG,CACnB,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,EACV,WAAW,EACX,YAAY,EACZ,mBAAmB,CACpB;AACD,MAAMC,YAAY,GAAG,CACnB,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,UAAU,EACV,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,CACb;AAED;;;AAGA,eAAc,MAAOC,MAAM;EAqCzBC,YACEC,QAA2C,EAC3CC,KAAsC,EACtCC,SAAyB;IAlC3B;IACQ,KAAAC,IAAI,GAA8B,IAAI;IAY9C;IACQ,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,gBAAgB,GAAwB,IAAI;IAC5C,KAAAC,eAAe,GAKnB;MACFvC,IAAI,EAAE,KAAK;MACXM,IAAI,EAAE,KAAK;MACXM,KAAK,EAAE,KAAK;MACZH,MAAM,EAAE;KACT;IAkYD,KAAA+B,QAAQ,GAAIC,CAAiB,IAAI;MAC/B;MACA,MAAMC,EAAE,GAAG,IAAI,CAACT,KAAK,CAACnB,WAAW,CAAC2B,CAAC,CAACE,IAAI,CAAC,CAAC;MAC1C,IAAID,EAAE,EAAE;QACNA,EAAE,CAACD,CAAC,CAAC;OACN,MAAM,IAAIA,CAAC,CAACE,IAAI,KAAK,OAAO,EAAE;QAC7BC,OAAO,CAACjB,KAAK,CAAEc,CAAsB,CAACd,KAAK,CAAC,CAAC,CAAC;;IAElD,CAAC;IA6CD,KAAAkB,eAAe,GAAIJ,CAA4C,IAAI;MACjE,IAAIA,CAAC,CAACE,IAAI,KAAK,WAAW,IAAIF,CAAC,CAACE,IAAI,KAAK,UAAU,EAAE;QACnD,IAAI,CAACG,YAAY,CAACL,CAAC,CAAC;;MAGtB;MACA,MAAMC,EAAE,GAAG,IAAI,CAACT,KAAK,CAAClD,aAAa,CAAC0D,CAAC,CAACE,IAAI,CAAC,CAAC;MAC5C,IAAID,EAAE,EAAE;QACN,IAAI,IAAI,CAACT,KAAK,CAACc,mBAAmB,IAAIN,CAAC,CAACE,IAAI,KAAK,WAAW,IAAIF,CAAC,CAACE,IAAI,KAAK,UAAU,EAAE;UACrFF,CAAC,CAACO,QAAQ,GAAG,IAAI,CAACV,gBAAgB,IAAI,IAAI,CAACW,sBAAsB,CAACR,CAAC,CAACS,KAAK,CAAC;;QAE5ER,EAAE,CAACD,CAAC,CAAC;QACL,OAAOA,CAAC,CAACO,QAAQ;;IAErB,CAAC;IAED,KAAAG,cAAc,GAAIV,CAA6B,IAAI;MACjD,IAAI,CAAC,IAAI,CAACL,eAAe,EAAE;QACzB;QACA,MAAMM,EAAE,GAAG,IAAI,CAACT,KAAK,CAACnC,YAAY,CAAC2C,CAAC,CAACE,IAAI,CAAC,CAAC;QAC3C,IAAID,EAAE,EAAE;UACNA,EAAE,CAACD,CAAC,CAAC;;;MAGT,IAAIA,CAAC,CAACE,IAAI,IAAI,IAAI,CAACJ,eAAe,EAAE;QAClC,IAAI,CAACA,eAAe,CAACE,CAAC,CAACE,IAAI,CAAC,GAAG,KAAK;;IAExC,CAAC;IAzcC,IAAI,CAACS,SAAS,GAAGpB,QAAQ;IACzB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACoB,WAAW,CAACnB,SAAS,CAAC;EAC7B;EAEA,IAAIoB,GAAGA,CAAA;IACL,OAAO,IAAI,CAACnB,IAAI;EAClB;EAEA,IAAIoB,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,gBAAgB;EAC9B;EAEAC,QAAQA,CAACxB,KAAsC;IAC7C,MAAMyB,QAAQ,GAAG,IAAI,CAACzB,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAElB,MAAM0B,eAAe,GAAG,IAAI,CAACC,eAAe,CAAC3B,KAAK,EAAEyB,QAAQ,CAAC;IAC7D,IAAIC,eAAe,EAAE;MACnB,IAAI,CAACE,sBAAsB,CAAC,IAAI,CAAC1B,IAAI,CAAC;;IAExC,MAAM2B,WAAW,GAAG,IAAI,CAACC,WAAW,CAAC9B,KAAK,CAAC;IAC3C,MAAM+B,gBAAgB,GAAG,IAAI,CAACC,gBAAgB,CAAChC,KAAK,EAAE,IAAI,CAAC;IAC3D,IAAI,CAACiC,YAAY,CAACjC,KAAK,EAAEyB,QAAQ,CAAC;IAClC,IAAI,CAACS,sBAAsB,CAAClC,KAAK,EAAEyB,QAAQ,CAAC;IAC5C,IAAI,CAACU,eAAe,CAACnC,KAAK,EAAEyB,QAAQ,CAAC;IAErC;IACA;IACA;IACA,IAAIC,eAAe,IAAIG,WAAW,IAAKE,gBAAgB,IAAI,CAAC,IAAI,CAAC7B,IAAI,CAACkC,QAAQ,EAAG,EAAE;MACjF,IAAI,CAACC,MAAM,EAAE;;EAEjB;EAEA,OAAOC,KAAKA,CACVtC,KAAsC,EACtCC,SAAyB;IAEzB,MAAMsC,IAAI,GAAG1C,MAAM,CAAC2C,SAAS,CAACC,GAAG,EAAsC;IACvE,IAAI,CAACF,IAAI,EAAE;MACT,OAAO,IAAI;;IAGb,MAAMlB,GAAG,GAAGkB,IAAI,CAAClB,GAAG;IACpB;IACA;IACA;IACA,MAAMqB,YAAY,GAAGrB,GAAG,CAACsB,YAAY,EAAE;IACvC1C,SAAS,CAAC2C,SAAS,GAAGF,YAAY,CAACE,SAAS;IAC5C,OAAOF,YAAY,CAACG,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC7C,SAAS,CAAC8C,WAAW,CAACL,YAAY,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC;;IAEnD;IACA;IACAxB,GAAG,CAAC2B,UAAU,GAAG/C,SAAS;IAE1B;IACA;IACA;IACA;IACA,MAAMgD,cAAc,GAAG5B,GAAG,CAAC6B,eAAe;IAC1C,IAAID,cAAc,EAAE;MAClBA,cAAc,CAACE,UAAU,EAAE;MAC3BF,cAAc,CAACG,OAAO,CAACnD,SAAS,CAAC;;IAGnC;IACAsC,IAAI,CAACf,QAAQ,CAAC;MAAC,GAAGxB,KAAK;MAAEqD,YAAY,EAAE;IAAK,CAAC,CAAC;IAC9ChC,GAAG,CAACnC,MAAM,EAAE;IACZ,MAAM;MAACoE;IAAgB,CAAC,GAAGtD,KAAK;IAChC,IAAIsD,gBAAgB,EAAE;MACpB,IAAIA,gBAAgB,CAACC,MAAM,EAAE;QAC3BlC,GAAG,CAACmC,SAAS,CAACF,gBAAgB,CAACC,MAAM,EAAE;UAAC,GAAGD,gBAAgB,CAACG,gBAAgB;UAAEC,QAAQ,EAAE;QAAC,CAAC,CAAC;OAC5F,MAAM;QACLnB,IAAI,CAACP,gBAAgB,CAACsB,gBAAgB,EAAE,KAAK,CAAC;;;IAIlD;IACA,IAAIjC,GAAG,CAACsC,aAAa,EAAE,EAAE;MACvBtC,GAAG,CAACuC,IAAI,CAAC,MAAM,CAAC;KACjB,MAAM;MACLvC,GAAG,CAACwC,IAAI,CAAC,WAAW,EAAE,MAAMxC,GAAG,CAACuC,IAAI,CAAC,MAAM,CAAC,CAAC;;IAG/C;IACA;IACAvC,GAAG,CAACyC,OAAO,EAAE;IACb,OAAOvB,IAAI;EACb;EAEA;EACAnB,WAAWA,CAACnB,SAAyB;IACnC,MAAM;MAACD;IAAK,CAAC,GAAG,IAAI;IACpB,MAAM;MAAC+D,QAAQ,GAAGrH;IAAa,CAAC,GAAGsD,KAAK;IACxC,MAAMgE,UAAU,GAAG;MACjB,GAAGhE,KAAK;MACR,GAAGA,KAAK,CAACsD,gBAAgB;MACzBW,WAAW,EAAEjE,KAAK,CAACkE,iBAAiB,IAAIC,qBAAqB,EAAE,IAAI,IAAI;MACvElE,SAAS;MACTmE,KAAK,EAAE5H,cAAc,CAACuH,QAAQ;KAC/B;IAED,MAAMM,SAAS,GAAGL,UAAU,CAACV,gBAAgB,IAAIU,UAAU,CAACK,SAAS,IAAIL,UAAU;IACnFM,MAAM,CAACC,MAAM,CAACP,UAAU,EAAE;MACxBQ,MAAM,EAAE,CAACH,SAAS,CAACI,SAAS,IAAI,CAAC,EAAEJ,SAAS,CAACK,QAAQ,IAAI,CAAC,CAAC;MAC3DrG,IAAI,EAAEgG,SAAS,CAAChG,IAAI,IAAI,CAAC;MACzBM,KAAK,EAAE0F,SAAS,CAAC1F,KAAK,IAAI,CAAC;MAC3BgG,OAAO,EAAEN,SAAS,CAACM,OAAO,IAAI;KAC/B,CAAC;IAEF,IAAI3E,KAAK,CAAC4E,EAAE,EAAE;MACZ;MACA,MAAMC,UAAU,GAAGC,iBAAiB,CAACC,SAAS,CAACF,UAAU;MACzD;MACA;MACA;MACAC,iBAAiB,CAACC,SAAS,CAACF,UAAU,GAAG,MAAK;QAC5C;QACAC,iBAAiB,CAACC,SAAS,CAACF,UAAU,GAAGA,UAAU;QACnD,OAAO7E,KAAK,CAAC4E,EAAE;MACjB,CAAC;;IAGH,MAAMvD,GAAG,GAAG,IAAI,IAAI,CAACF,SAAS,CAAC6C,UAAU,CAA8B;IACvE;IACA,IAAIK,SAAS,CAACW,OAAO,EAAE;MACrB3D,GAAG,CAAC4D,UAAU,CAACZ,SAAS,CAACW,OAAO,CAAC;;IAEnC,IAAIhF,KAAK,CAACkF,MAAM,EAAE;MAChB7D,GAAG,CAAC8D,SAAS,EAAE,CAACf,KAAK,CAACc,MAAM,GAAGlF,KAAK,CAACkF,MAAM;;IAE7C,IAAI,CAACtD,sBAAsB,CAACP,GAAG,CAAC;IAEhC;IACA;IACA,MAAM+D,SAAS,GAAG/D,GAAG,CAACgE,OAAO;IAC7BhE,GAAG,CAACgE,OAAO,GAAIC,GAAW,IAAI;MAC5B,IAAI,CAAClF,SAAS,GAAG,IAAI;MACrBgF,SAAS,CAACG,IAAI,CAAClE,GAAG,EAAEiE,GAAG,CAAC;MACxB,IAAI,CAAClF,SAAS,GAAG,KAAK;IACxB,CAAC;IACD,MAAMoF,kBAAkB,GAAGnE,GAAG,CAACoE,gBAAgB,CAACC,GAAG;IACnDrE,GAAG,CAACoE,gBAAgB,CAACC,GAAG,GAAIJ,GAAW,IAAI;MACzCE,kBAAkB,CAACD,IAAI,CAAClE,GAAG,CAACoE,gBAAgB,EAAEH,GAAG,CAAC;MAClD,IAAI,CAACK,gBAAgB,EAAE;IACzB,CAAC;IACDtE,GAAG,CAACuE,EAAE,CAAC,QAAQ,EAAE,MAAM,IAAI,CAACC,eAAe,EAAE,CAAC;IAC9C;IACA;IACA,MAAMC,SAAS,GAAGzE,GAAG,CAACuC,IAAI;IAC1BvC,GAAG,CAACuC,IAAI,GAAG,IAAI,CAACmC,UAAU,CAACC,IAAI,CAAC,IAAI,EAAEF,SAAS,CAAC;IAEhD;IACAzE,GAAG,CAACuE,EAAE,CAAC,QAAQ,EAAE,MAAK;MACpB,IAAI,CAACrE,gBAAgB,CAACrC,MAAM,CAACmC,GAAG,CAACC,SAAS,CAAC2E,KAAK,EAAE5E,GAAG,CAACC,SAAS,CAAC4E,MAAM,CAAC;IACzE,CAAC,CAAC;IACF7E,GAAG,CAACuE,EAAE,CAAC,WAAW,EAAE,MAAK;MACvB,IAAI,CAAC1D,sBAAsB,CAAC,IAAI,CAAClC,KAAK,EAAE,EAAE,CAAC;MAC3C;MACAzD,cAAc,CAAC8E,GAAG,CAACC,SAAS,EAAE,IAAI,CAACC,gBAAgB,CAAC;IACtD,CAAC,CAAC;IACFF,GAAG,CAACuE,EAAE,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC1D,sBAAsB,CAAC,IAAI,CAAClC,KAAK,EAAE,EAAE,CAAC,CAAC;IACvE,KAAK,MAAMmG,SAAS,IAAIrJ,aAAa,EAAE;MACrCuE,GAAG,CAACuE,EAAE,CAACO,SAAS,EAAE,IAAI,CAACvF,eAAe,CAAC;;IAEzC,KAAK,MAAMuF,SAAS,IAAItI,YAAY,EAAE;MACpCwD,GAAG,CAACuE,EAAE,CAACO,SAAS,EAAE,IAAI,CAACjF,cAAc,CAAC;;IAExC,KAAK,MAAMiF,SAAS,IAAItH,WAAW,EAAE;MACnCwC,GAAG,CAACuE,EAAE,CAACO,SAAS,EAAE,IAAI,CAAC5F,QAAQ,CAAC;;IAElC,IAAI,CAACL,IAAI,GAAGmB,GAAG;EACjB;EACA;EAEA+E,OAAOA,CAAA;IACL;IACA,MAAMnG,SAAS,GAAG,IAAI,CAACoB,GAAG,CAACsB,YAAY,EAAE;IACzC,MAAM0D,QAAQ,GAAGpG,SAAS,CAACqG,aAAa,CAAC,qBAAqB,CAAC;IAC/DD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE/G,MAAM,EAAE;IAElBO,MAAM,CAAC2C,SAAS,CAAC+D,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACtG,IAAI,CAACZ,MAAM,EAAE;EACpB;EAEA;EACA;EACA;EACA+C,MAAMA,CAAA;IACJ,MAAMhB,GAAG,GAAG,IAAI,CAACnB,IAAW;IAC5B;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACE,SAAS,IAAIiB,GAAG,CAAC+C,KAAK,EAAE;MAChC;MACA,IAAI/C,GAAG,CAACoF,MAAM,EAAE;QACdpF,GAAG,CAACoF,MAAM,CAACC,MAAM,EAAE;QACnBrF,GAAG,CAACoF,MAAM,GAAG,IAAI;;MAEnB;MACApF,GAAG,CAACgE,OAAO,EAAE;;EAEjB;EAEAzD,sBAAsBA,CAACP,GAAQ;IAC7B,MAAMsF,eAAe,GAAGrK,cAAc,CAAC+E,GAAG,CAACC,SAAS,CAAC;IACrDD,GAAG,CAACuF,OAAO,CAACtF,SAAS,GAAGqF,eAAe;IAEvC,IAAI,CAACpF,gBAAgB,GAAGoF,eAAe;EACzC;EAEA;;;;EAIA7E,WAAWA,CAAC+E,SAA8B;IACxC;IACA,MAAM;MAACxC;IAAS,CAAC,GAAGwC,SAAS;IAC7B,IAAIxC,SAAS,EAAE;MACb,MAAMhD,GAAG,GAAG,IAAI,CAACnB,IAAI;MACrB,IAAImE,SAAS,CAAC4B,KAAK,KAAK5E,GAAG,CAACC,SAAS,CAAC2E,KAAK,IAAI5B,SAAS,CAAC6B,MAAM,KAAK7E,GAAG,CAACC,SAAS,CAAC4E,MAAM,EAAE;QACxF7E,GAAG,CAACnC,MAAM,EAAE;QACZ,OAAO,IAAI;;;IAGf,OAAO,KAAK;EACd;EAEA;EACA;;;;;EAKA8C,gBAAgBA,CAAC6E,SAA8B,EAAEC,aAAsB;IACrE,IAAI,IAAI,CAAC3G,eAAe,EAAE;MACxB,OAAO,KAAK;;IAEd,MAAMkB,GAAG,GAAG,IAAI,CAACnB,IAAI;IAErB,MAAM6G,EAAE,GAAG,IAAI,CAACxF,gBAAgB;IAChC;IACA,MAAM;MAAClD,IAAI;MAAEM,KAAK;MAAEgG;IAAO,CAAC,GAAGoC,EAAE;IACjC,MAAM3E,QAAQ,GAAGf,GAAG,CAACe,QAAQ,EAAE;IAE/B,IAAIA,QAAQ,EAAE;MACZ;MACA2E,EAAE,CAACC,wBAAwB,GAAG,KAAK;;IAErC,MAAMC,OAAO,GAAG5K,yBAAyB,CAAC0K,EAAE,EAAE;MAC5C,GAAG3K,oBAAoB,CAACiF,GAAG,CAACC,SAAS,CAAC;MACtC,GAAGuF;KACJ,CAAC;IACF,IAAIzE,QAAQ,EAAE;MACZ;MACA2E,EAAE,CAACC,wBAAwB,GAAG,QAAQ;;IAGxC,IAAIC,OAAO,IAAIH,aAAa,EAAE;MAC5B,MAAMI,cAAc,GAAG,IAAI,CAAC5G,eAAe;MAC3C;MACA4G,cAAc,CAACnJ,IAAI,GAAG,IAAI;MAC1BmJ,cAAc,CAAC7I,IAAI,KAAnB6I,cAAc,CAAC7I,IAAI,GAAKA,IAAI,KAAK0I,EAAE,CAAC1I,IAAI;MACxC6I,cAAc,CAAC1I,MAAM,KAArB0I,cAAc,CAAC1I,MAAM,GAAKmG,OAAO,KAAKoC,EAAE,CAACpC,OAAO;MAChDuC,cAAc,CAACvI,KAAK,KAApBuI,cAAc,CAACvI,KAAK,GAAKA,KAAK,KAAKoI,EAAE,CAACpI,KAAK;;IAG7C;IACA;IACA,IAAI,CAACyD,QAAQ,EAAE;MACb/F,yBAAyB,CAACgF,GAAG,CAACC,SAAS,EAAEuF,SAAS,CAAC;;IAGrD,OAAOI,OAAO;EAChB;EAEA;;;;;EAKAtF,eAAeA,CAACkF,SAA8B,EAAEM,SAA8B;IAC5E,MAAM9F,GAAG,GAAG,IAAI,CAACnB,IAAI;IACrB,IAAI+G,OAAO,GAAG,KAAK;IACnB,KAAK,MAAMG,QAAQ,IAAIzH,YAAY,EAAE;MACnC,IAAIyH,QAAQ,IAAIP,SAAS,IAAI,CAACpK,SAAS,CAACoK,SAAS,CAACO,QAAQ,CAAC,EAAED,SAAS,CAACC,QAAQ,CAAC,CAAC,EAAE;QACjFH,OAAO,GAAG,IAAI;QACd,MAAMI,MAAM,GAAGhG,GAAG,CAAC,MAAM+F,QAAQ,CAAC,CAAC,CAAC,CAACE,WAAW,EAAE,GAAGF,QAAQ,CAACG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QACzEF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE9B,IAAI,CAAClE,GAAG,EAAEwF,SAAS,CAACO,QAAQ,CAAC,CAAC;;;IAG1C,OAAOH,OAAO;EAChB;EAEA;;;;;EAKAhF,YAAYA,CAAC4E,SAA8B,EAAEM,SAA8B;IACzE,IAAIN,SAAS,CAAC3B,MAAM,KAAKiC,SAAS,CAACjC,MAAM,EAAE;MACzC,IAAI,CAAChF,IAAI,CAACiF,SAAS,EAAE,CAACf,KAAK,CAACc,MAAM,GAAG2B,SAAS,CAAC3B,MAAM,IAAI,EAAE;;IAE7D,IAAI2B,SAAS,CAAC9C,QAAQ,KAAKoD,SAAS,CAACpD,QAAQ,EAAE;MAC7C,MAAM;QAACA,QAAQ,GAAGrH,aAAa;QAAE2G,YAAY,GAAG;MAAI,CAAC,GAAGwD,SAAS;MACjE,MAAMW,OAAO,GAAQ;QACnBC,IAAI,EAAEpE;OACP;MACD,IAAI,0BAA0B,IAAIwD,SAAS,EAAE;QAC3C;QACAW,OAAO,CAACE,wBAAwB,GAAGb,SAAS,CAACa,wBAAwB;;MAEvE,IAAI,CAACxH,IAAI,CAACyH,QAAQ,CAACnL,cAAc,CAACuH,QAAQ,CAAC,EAAEyD,OAAO,CAAC;MACrD,OAAO,IAAI;;IAEb,OAAO,KAAK;EACd;EAEA;;;;;EAKAtF,sBAAsBA,CAAC2E,SAA8B,EAAEM,SAA8B;IACnF,MAAM9F,GAAG,GAAG,IAAI,CAACnB,IAAI;IACrB,IAAI+G,OAAO,GAAG,KAAK;IACnB,IAAI5F,GAAG,CAACsC,aAAa,EAAE,EAAE;MACvB,IAAI,OAAO,IAAIkD,SAAS,IAAIxF,GAAG,CAACuG,QAAQ,IAAI,CAACnL,SAAS,CAACoK,SAAS,CAACgB,KAAK,EAAEV,SAAS,CAACU,KAAK,CAAC,EAAE;QACxFZ,OAAO,GAAG,IAAI;QACd5F,GAAG,CAACuG,QAAQ,CAACf,SAAS,CAACgB,KAAK,CAAC;;MAE/B,IAAI,KAAK,IAAIhB,SAAS,IAAIxF,GAAG,CAACyG,MAAM,IAAI,CAACrL,SAAS,CAACoK,SAAS,CAACkB,GAAG,EAAEZ,SAAS,CAACY,GAAG,CAAC,EAAE;QAChFd,OAAO,GAAG,IAAI;QACd5F,GAAG,CAACyG,MAAM,CAACjB,SAAS,CAACkB,GAAG,CAAC;;MAE3B,IACE,SAAS,IAAIlB,SAAS,IACtBxF,GAAG,CAAC2G,UAAU,IACd,CAACvL,SAAS,CAACoK,SAAS,CAACoB,OAAO,EAAEd,SAAS,CAACc,OAAO,CAAC,EAChD;QACA,IAAI,CAACpB,SAAS,CAACoB,OAAO,IAAI5G,GAAG,CAAC6G,SAAS,CAACrB,SAAS,CAACoB,OAAO,CAACE,MAAM,CAAC,EAAE;UACjElB,OAAO,GAAG,IAAI;UACd5F,GAAG,CAAC2G,UAAU,CAACnB,SAAS,CAACoB,OAAO,CAAC;;;;IAIvC,OAAOhB,OAAO;EAChB;EAEA;;;;;EAKA9E,eAAeA,CAAC0E,SAA8B,EAAEM,SAA8B;;IAC5E,MAAM9F,GAAG,GAAG,IAAI,CAACnB,IAAI;IACrB,IAAI+G,OAAO,GAAG,KAAK;IACnB,KAAK,MAAMG,QAAQ,IAAIxH,YAAY,EAAE;MACnC,MAAMwI,QAAQ,GAAG,CAAAC,EAAA,GAAAxB,SAAS,CAACO,QAAQ,CAAC,cAAAiB,EAAA,cAAAA,EAAA,GAAI,IAAI;MAC5C,MAAMC,QAAQ,GAAG,CAAAC,EAAA,GAAApB,SAAS,CAACC,QAAQ,CAAC,cAAAmB,EAAA,cAAAA,EAAA,GAAI,IAAI;MAC5C,IAAI,CAAC9L,SAAS,CAAC2L,QAAQ,EAAEE,QAAQ,CAAC,EAAE;QAClCrB,OAAO,GAAG,IAAI;QACd,IAAImB,QAAQ,EAAE;UACZ/G,GAAG,CAAC+F,QAAQ,CAAC,CAACoB,MAAM,CAACJ,QAAQ,CAAC;SAC/B,MAAM;UACL/G,GAAG,CAAC+F,QAAQ,CAAC,CAACqB,OAAO,EAAE;;;;IAI7B,OAAOxB,OAAO;EAChB;EAYQjG,sBAAsBA,CAACC,KAAY;IACzC,MAAMI,GAAG,GAAG,IAAI,CAACnB,IAAI;IACrB,MAAM6G,EAAE,GAAG1F,GAAG,CAACC,SAAS;IACxB,MAAM;MAACR,mBAAmB,GAAG;IAAE,CAAC,GAAG,IAAI,CAACd,KAAK;IAC7C,IAAI;MACFqB,GAAG,CAACC,SAAS,GAAG,IAAI,CAACC,gBAAgB;MACrC,OAAOF,GAAG,CAACqH,qBAAqB,CAACzH,KAAK,EAAE;QACtCpE,MAAM,EAAEiE,mBAAmB,CAAC6H,MAAM,CAACtH,GAAG,CAACuH,QAAQ,CAAC5C,IAAI,CAAC3E,GAAG,CAAC;OAC1D,CAAC;KACH,CAAC,OAAAgH,EAAA,EAAM;MACN;MACA,OAAO,EAAE;KACV,SAAS;MACRhH,GAAG,CAACC,SAAS,GAAGyF,EAAE;;EAEtB;EAEAlG,YAAYA,CAACL,CAAsB;;IACjC,MAAM;MAACR;IAAK,CAAC,GAAG,IAAI;IACpB,MAAM6I,0BAA0B,GAC9B7I,KAAK,CAACc,mBAAmB,KAAKd,KAAK,CAAC8I,WAAW,IAAI9I,KAAK,CAAC+I,YAAY,IAAI/I,KAAK,CAACgJ,YAAY,CAAC;IAE9F,IAAIH,0BAA0B,EAAE;MAC9B,MAAMI,SAAS,GAAGzI,CAAC,CAACE,IAAI;MACxB,MAAMwI,WAAW,GAAG,EAAAb,EAAA,OAAI,CAAChI,gBAAgB,cAAAgI,EAAA,uBAAAA,EAAA,CAAEvF,MAAM,IAAG,CAAC;MACrD,MAAM/B,QAAQ,GAAG,IAAI,CAACC,sBAAsB,CAACR,CAAC,CAACS,KAAK,CAAC;MACrD,MAAMkI,UAAU,GAAGpI,QAAQ,CAAC+B,MAAM,GAAG,CAAC;MAEtC,IAAI,CAACqG,UAAU,IAAID,WAAW,EAAE;QAC9B1I,CAAC,CAACE,IAAI,GAAG,YAAY;QACrB,IAAI,CAACE,eAAe,CAACJ,CAAC,CAAC;;MAEzB,IAAI,CAACH,gBAAgB,GAAGU,QAAQ;MAChC,IAAIoI,UAAU,IAAI,CAACD,WAAW,EAAE;QAC9B1I,CAAC,CAACE,IAAI,GAAG,YAAY;QACrB,IAAI,CAACE,eAAe,CAACJ,CAAC,CAAC;;MAEzBA,CAAC,CAACE,IAAI,GAAGuI,SAAS;KACnB,MAAM;MACL,IAAI,CAAC5I,gBAAgB,GAAG,IAAI;;EAEhC;EA+BA0F,UAAUA,CAACqD,QAAkB,EAAEC,KAA8B,EAAEC,UAAmB;IAChF,MAAMjI,GAAG,GAAG,IAAI,CAACnB,IAAI;IACrB,MAAM6G,EAAE,GAAG1F,GAAG,CAACC,SAAS;IAExB,MAAM2H,SAAS,GAAG,OAAOI,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC3I,IAAI;IAChE,IAAIuI,SAAS,KAAK,MAAM,EAAE;MACxB,IAAI,CAACjH,gBAAgB,CAAC,IAAI,CAAChC,KAAK,EAAE,KAAK,CAAC;;IAE1C,IAAIiJ,SAAS,IAAIpL,YAAY,EAAE;MAC7B,IAAI,OAAOwL,KAAK,KAAK,QAAQ,EAAE;QAC5BA,KAA+C,CAAChF,SAAS,GAAGjI,oBAAoB,CAAC2K,EAAE,CAAC;;MAEvF,IAAI,IAAI,CAAC7G,IAAI,CAACkC,QAAQ,EAAE,EAAE;QACxB;QACAf,GAAG,CAACC,SAAS,GAAG,IAAI,CAACC,gBAAgB;QACrC6H,QAAQ,CAAC7D,IAAI,CAAClE,GAAG,EAAEgI,KAAK,EAAEC,UAAU,CAAC;QACrCjI,GAAG,CAACC,SAAS,GAAGyF,EAAE;QAElB,OAAO1F,GAAG;;;IAGd+H,QAAQ,CAAC7D,IAAI,CAAClE,GAAG,EAAEgI,KAAK,EAAEC,UAAU,CAAC;IAErC,OAAOjI,GAAG;EACZ;EAEA;EACAsE,gBAAgBA,CAAA;IACd,MAAMtE,GAAG,GAAG,IAAI,CAACnB,IAAI;IAErB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,KAAK,MAAM8I,SAAS,IAAI,IAAI,CAAC3I,eAAe,EAAE;MAC5C,IAAI,IAAI,CAACA,eAAe,CAAC2I,SAAS,CAAC,EAAE;QACnC5H,GAAG,CAACuC,IAAI,CAACqF,SAAS,CAAC;;;IAGvB,IAAI,CAAC9I,eAAe,GAAG,KAAK;IAE5B,MAAM4G,EAAE,GAAG,IAAI,CAAC7G,IAAI,CAACoB,SAAS;IAC9B;IACAD,GAAG,CAACC,SAAS,GAAG,IAAI,CAACC,gBAAgB;IAErC,IAAI,CAACsE,eAAe,GAAG,MAAK;MAC1B;MACA;MACAtJ,cAAc,CAAC,IAAI,CAACgF,gBAAgB,EAAEwF,EAAE,CAAC;MACzC;MACA1F,GAAG,CAACC,SAAS,GAAGyF,EAAE;IACpB,CAAC;EACH;;AApgBOlH,MAAA,CAAA2C,SAAS,GAAa,EAAE;AAygBjC;;;;;;;;AAQA,SAAS2B,qBAAqBA,CAAA;EAC5B,IAAIF,WAAW,GAAG,IAAI;EAEtB;EACA,IAAI,OAAOsF,QAAQ,KAAK,WAAW,EAAE;IACnC,MAAMC,KAAK,GAAG,wBAAwB,CAACC,IAAI,CAACF,QAAQ,CAACG,MAAM,CAAC;IAC5DzF,WAAW,GAAGuF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;;EAGjC;EACA,IAAI;IACFvF,WAAW,GAAGA,WAAW,IAAI0F,OAAO,CAACC,GAAG,CAACC,iBAAiB;GAC3D,CAAC,OAAAxB,EAAA,EAAM;IACN;EAAA;EAGF,IAAI;IACFpE,WAAW,GAAGA,WAAW,IAAI0F,OAAO,CAACC,GAAG,CAACE,6BAA6B;GACvE,CAAC,OAAAvB,EAAA,EAAM;IACN;EAAA;EAGF,OAAOtE,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}