import React, { useState, useEffect } from 'react';
import Map, { Source, Layer } from 'react-map-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

const MoroccoMap = () => {
  const [tileServerRunning, setTileServerRunning] = useState(false);
  const [loading, setLoading] = useState(true);
  const [viewState, setViewState] = useState({
    longitude: -7.0926,
    latitude: 31.7917,
    zoom: 5,
    pitch: 0,
    bearing: 0
  });

  // Vérifier si le serveur de tuiles est en cours d'exécution
  useEffect(() => {
    const checkTileServer = async () => {
      try {
        const response = await fetch('http://localhost:3001/tiles/metadata');
        if (response.ok) {
          setTileServerRunning(true);
          console.log('✅ Serveur de tuiles détecté');
        }
      } catch (error) {
        console.log('⚠️ Serveur de tuiles non disponible');
        setTileServerRunning(false);
      } finally {
        setLoading(false);
      }
    };

    checkTileServer();
  }, []);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh', 
        backgroundColor: '#f3f4f6' 
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #2563eb',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280' }}>Chargement des tuiles vectorielles...</p>
        </div>
      </div>
    );
  }

  // Style de carte personnalisé pour les tuiles locales
  const customMapStyle = {
    version: 8,
    sources: {
      'raster-tiles': {
        type: 'raster',
        tiles: tileServerRunning ?
          ['http://localhost:3001/tiles/raster/{z}/{x}/{y}.png'] :
          ['https://tile.openstreetmap.org/{z}/{x}/{y}.png'],
        tileSize: 256,
        minzoom: 0,
        maxzoom: 14
      }
    },
    layers: [
      {
        id: 'background',
        type: 'background',
        paint: {
          'background-color': '#f0f8ff'
        }
      },
      {
        id: 'raster-layer',
        type: 'raster',
        source: 'raster-tiles',
        paint: {
          'raster-opacity': 0.8
        }
      }
    ]
  };

  // Style des couches vectorielles
  const countryLayerStyle = {
    id: 'countries-fill',
    type: 'fill',
    paint: {
      'fill-color': [
        'case',
        ['==', ['get', 'name'], 'Morocco'],
        '#2563eb', // Bleu pour le Maroc
        '#f3f4f6'  // Gris clair pour les autres pays
      ],
      'fill-opacity': 0.6
    }
  };

  const countryBorderStyle = {
    id: 'countries-border',
    type: 'line',
    paint: {
      'line-color': [
        'case',
        ['==', ['get', 'name'], 'Morocco'],
        '#1d4ed8',
        '#6b7280'
      ],
      'line-width': [
        'case',
        ['==', ['get', 'name'], 'Morocco'],
        3,
        1
      ]
    }
  };

  return (
    <div style={{ height: '100vh', width: '100%' }}>
      <Map
        {...viewState}
        onMove={evt => setViewState(evt.viewState)}
        style={{ width: '100%', height: '100%' }}
        mapStyle={customMapStyle}
        mapboxAccessToken="pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw"
      >
        {/* Tuiles vectorielles du serveur local */}
        {tileServerRunning && (
          <Source
            id="vector-tiles-server"
            type="geojson"
            data="http://localhost:3001/tiles/vector/0/0/0.json"
          >
            <Layer {...countryLayerStyle} />
            <Layer {...countryBorderStyle} />
          </Source>
        )}
      </Map>

      {/* Panneau d'informations */}
      <div style={{
        position: 'absolute',
        top: '16px',
        left: '16px',
        backgroundColor: 'white',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        maxWidth: '320px',
        zIndex: 1000
      }}>
        <h2 style={{
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1f2937',
          marginBottom: '8px'
        }}>
          🗺️ Carte Vectorielle du Maroc
        </h2>
        <p style={{
          fontSize: '14px',
          color: '#6b7280',
          marginBottom: '8px'
        }}>
          Région incluant le Maroc, l'Europe du Sud, l'Algérie, la Mauritanie et l'océan Atlantique
        </p>

        {/* Statut du serveur de tuiles */}
        <div style={{
          fontSize: '12px',
          marginBottom: '8px',
          padding: '4px 8px',
          borderRadius: '4px',
          backgroundColor: tileServerRunning ? '#dcfce7' : '#fef3c7',
          color: tileServerRunning ? '#166534' : '#92400e'
        }}>
          {tileServerRunning ? '🟢 Mode Hors Ligne Actif' : '🟡 Mode En Ligne (OpenStreetMap)'}
        </div>

        {tileServerRunning && (
          <div style={{ fontSize: '12px', color: '#9ca3af' }}>
            <p>✅ Tuiles locales chargées</p>
            <p>🌐 Fonctionne sans internet</p>
            <p>📊 Zoom: {Math.round(viewState.zoom * 10) / 10}</p>
          </div>
        )}
      </div>

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default MoroccoMap;
