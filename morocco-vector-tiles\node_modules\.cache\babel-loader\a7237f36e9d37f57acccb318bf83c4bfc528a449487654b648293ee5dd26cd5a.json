{"ast": null, "code": "// From https://github.com/streamich/react-use/blob/master/src/useIsomorphicLayoutEffect.ts\n// useLayoutEffect but does not trigger warning in server-side rendering\nimport { useEffect, useLayoutEffect } from 'react';\nconst useIsomorphicLayoutEffect = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect", "document"], "sources": ["C:\\Users\\<USER>\\Downloads\\morocco-complete-map-geojson-topojson-main\\morocco-vector-tiles\\node_modules\\react-map-gl\\src\\utils\\use-isomorphic-layout-effect.ts"], "sourcesContent": ["// From https://github.com/streamich/react-use/blob/master/src/useIsomorphicLayoutEffect.ts\n// useLayoutEffect but does not trigger warning in server-side rendering\nimport {useEffect, useLayoutEffect} from 'react';\n\nconst useIsomorphicLayoutEffect = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\nexport default useIsomorphicLayoutEffect;\n"], "mappings": "AAAA;AACA;AACA,SAAQA,SAAS,EAAEC,eAAe,QAAO,OAAO;AAEhD,MAAMC,yBAAyB,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGF,eAAe,GAAGD,SAAS;AAE/F,eAAeE,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}